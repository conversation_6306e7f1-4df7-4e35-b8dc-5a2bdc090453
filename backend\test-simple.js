// Test simple du serveur
console.log('🚀 Démarrage du test serveur...');

try {
  // Test des modules de base
  const express = require('express');
  console.log('✅ Express chargé');
  
  const cors = require('cors');
  console.log('✅ CORS chargé');
  
  require('dotenv').config();
  console.log('✅ Variables d\'environnement chargées');
  console.log('PORT:', process.env.PORT || 5000);
  console.log('NODE_ENV:', process.env.NODE_ENV || 'development');
  
  // Créer une app Express simple
  const app = express();
  const PORT = process.env.PORT || 5000;
  
  // Middleware de base
  app.use(cors());
  app.use(express.json());
  
  // Route de test
  app.get('/api/health', (req, res) => {
    res.json({
      message: 'API Espoir Sportif de Chorbane fonctionne !',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    });
  });
  
  app.get('/api/test', (req, res) => {
    res.json({
      message: 'Test endpoint',
      status: 'OK'
    });
  });
  
  // Démarrer le serveur
  const server = app.listen(PORT, () => {
    console.log(`🌟 Serveur de test démarré sur le port ${PORT}`);
    console.log(`🔗 API disponible sur: http://localhost:${PORT}/api`);
    console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
  });
  
  // Gestion des erreurs
  server.on('error', (error) => {
    console.error('❌ Erreur serveur:', error.message);
  });
  
  // Gestion de l'arrêt propre
  process.on('SIGINT', () => {
    console.log('\n🛑 Arrêt du serveur...');
    server.close(() => {
      console.log('✅ Serveur arrêté proprement');
      process.exit(0);
    });
  });
  
} catch (error) {
  console.error('❌ Erreur lors du démarrage:', error.message);
  console.error('Stack:', error.stack);
  process.exit(1);
}
