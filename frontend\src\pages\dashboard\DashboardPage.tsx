import React from 'react';
import {
  Users,
  Calendar,
  FileText,
  Trophy,
  TrendingUp,
  Clock,
  Target,
  Award
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

const DashboardPage: React.FC = () => {
  const { user, isAdmin, isPlayer, isSupporter } = useAuth();

  // Mock data - In real app, fetch from API
  const stats = {
    totalPlayers: 29,
    totalMatches: 25,
    totalNews: 15,
    totalReservations: 142,
    nextMatch: {
      opponent: 'AS Soliman',
      date: '2025-05-28',
      time: '15:00',
      location: 'Stade Municipal Chorbane'
    },
    recentNews: [
      { id: 1, title: 'Victoire éclatante contre le CA Bizertin', date: '2025-05-20' },
      { id: 2, title: 'Nouveau joueur: Hamza Bedoui rejoint l\'équipe', date: '2025-05-18' },
      { id: 3, title: 'Préparation intensive pour le prochain match', date: '2025-05-15' }
    ]
  };

  const getWelcomeMessage = () => {
    const hour = new Date().getHours();
    let greeting = 'Bonjour';
    if (hour >= 18) greeting = 'Bonsoir';
    else if (hour >= 12) greeting = 'Bon après-midi';

    return `${greeting}, ${user?.firstName} !`;
  };

  const renderAdminDashboard = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Joueurs</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalPlayers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Matchs</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalMatches}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <FileText className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Actualités</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalNews}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Trophy className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Réservations</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalReservations}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <a href="/dashboard/players/new" className="btn btn-primary">
            <Users className="h-5 w-5 mr-2" />
            Ajouter un joueur
          </a>
          <a href="/dashboard/matches/new" className="btn btn-outline">
            <Calendar className="h-5 w-5 mr-2" />
            Programmer un match
          </a>
          <a href="/dashboard/news/new" className="btn btn-outline">
            <FileText className="h-5 w-5 mr-2" />
            Publier une actualité
          </a>
        </div>
      </div>
    </div>
  );

  const renderPlayerDashboard = () => (
    <div className="space-y-6">
      {/* Player Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Target className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Buts marqués</p>
              <p className="text-2xl font-bold text-gray-900">8</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Matchs joués</p>
              <p className="text-2xl font-bold text-gray-900">15</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Award className="h-6 w-6 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Passes décisives</p>
              <p className="text-2xl font-bold text-gray-900">5</p>
            </div>
          </div>
        </div>
      </div>

      {/* Player Info */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Informations joueur</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm text-gray-600">Numéro de maillot</p>
            <p className="text-lg font-medium">#{user?.player?.jerseyNumber}</p>
          </div>
          <div>
            <p className="text-sm text-gray-600">Position</p>
            <p className="text-lg font-medium">{user?.player?.position}</p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSupporterDashboard = () => (
    <div className="space-y-6">
      {/* Supporter Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Trophy className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Matchs assistés</p>
              <p className="text-2xl font-bold text-gray-900">12</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Calendar className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Réservations</p>
              <p className="text-2xl font-bold text-gray-900">3</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Actions rapides</h3>
        <div className="space-y-3">
          <a href="/dashboard/reservations/new" className="btn btn-primary w-full">
            Réserver des billets
          </a>
          <a href="/matches" className="btn btn-outline w-full">
            Voir les prochains matchs
          </a>
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">{getWelcomeMessage()}</h1>
          <p className="mt-1 text-sm text-gray-600">
            Voici un aperçu de votre espace Espoir Sportif de Chorbane
          </p>
        </div>

        {/* Next Match Card */}
        <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg shadow-lg p-6 text-white mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-medium mb-2">Prochain match</h3>
              <p className="text-2xl font-bold">Espoir Sportif de Chorbane vs {stats.nextMatch.opponent}</p>
              <div className="flex items-center mt-2 space-x-4">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-sm">{stats.nextMatch.date} à {stats.nextMatch.time}</span>
                </div>
                <span className="text-sm">{stats.nextMatch.location}</span>
              </div>
            </div>
            <div className="text-right">
              <a href="/reservations" className="btn bg-white text-green-600 hover:bg-gray-100">
                Réserver
              </a>
            </div>
          </div>
        </div>

        {/* Role-specific content */}
        {isAdmin && renderAdminDashboard()}
        {isPlayer && renderPlayerDashboard()}
        {isSupporter && renderSupporterDashboard()}

        {/* Recent News */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Dernières actualités</h3>
          <div className="space-y-3">
            {stats.recentNews.map((news) => (
              <div key={news.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                <div>
                  <p className="text-sm font-medium text-gray-900">{news.title}</p>
                  <p className="text-xs text-gray-500">{news.date}</p>
                </div>
                <a href={`/news/${news.id}`} className="text-green-600 hover:text-green-500 text-sm">
                  Lire →
                </a>
              </div>
            ))}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DashboardPage;
