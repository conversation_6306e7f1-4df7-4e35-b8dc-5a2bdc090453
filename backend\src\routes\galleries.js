const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// GET /api/galleries - Récupérer toutes les galeries (public)
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 12 } = req.query;
    const skip = (page - 1) * limit;

    const [galleries, total] = await Promise.all([
      prisma.gallery.findMany({
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          media: {
            take: 1, // Prendre seulement le premier média comme aperçu
            orderBy: {
              createdAt: 'asc'
            }
          },
          _count: {
            select: {
              media: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.gallery.count()
    ]);

    res.json({
      message: 'Galeries récupérées avec succès',
      galleries,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des galeries:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/galleries/:id - Récupérer une galerie par ID (public)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const gallery = await prisma.gallery.findUnique({
      where: { id },
      include: {
        media: {
          orderBy: {
            createdAt: 'asc'
          }
        }
      }
    });

    if (!gallery) {
      return res.status(404).json({
        message: 'Galerie non trouvée'
      });
    }

    res.json({
      message: 'Galerie récupérée avec succès',
      gallery
    });

  } catch (error) {
    console.error('Erreur lors de la récupération de la galerie:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/galleries - Créer une nouvelle galerie (Admin seulement)
router.post('/', [
  body('title')
    .notEmpty()
    .withMessage('Titre de la galerie requis'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('La description ne peut pas dépasser 500 caractères')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { title, description, coverImage } = req.body;

    const gallery = await prisma.gallery.create({
      data: {
        title,
        description,
        coverImage
      }
    });

    res.status(201).json({
      message: 'Galerie créée avec succès',
      gallery
    });

  } catch (error) {
    console.error('Erreur lors de la création de la galerie:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// PUT /api/galleries/:id - Mettre à jour une galerie (Admin seulement)
router.put('/:id', [
  body('title')
    .optional()
    .notEmpty()
    .withMessage('Titre de la galerie requis'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('La description ne peut pas dépasser 500 caractères')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, description, coverImage } = req.body;

    // Vérifier que la galerie existe
    const existingGallery = await prisma.gallery.findUnique({
      where: { id }
    });

    if (!existingGallery) {
      return res.status(404).json({
        message: 'Galerie non trouvée'
      });
    }

    const updatedGallery = await prisma.gallery.update({
      where: { id },
      data: {
        title,
        description,
        coverImage
      },
      include: {
        media: true
      }
    });

    res.json({
      message: 'Galerie mise à jour avec succès',
      gallery: updatedGallery
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour de la galerie:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/galleries/:id/media - Ajouter des médias à une galerie (Admin seulement)
router.post('/:id/media', [
  body('media')
    .isArray()
    .withMessage('Liste de médias requise')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { media } = req.body;

    // Vérifier que la galerie existe
    const gallery = await prisma.gallery.findUnique({
      where: { id }
    });

    if (!gallery) {
      return res.status(404).json({
        message: 'Galerie non trouvée'
      });
    }

    // Ajouter les médias
    const createdMedia = await Promise.all(
      media.map(({ fileName, filePath, fileType, caption }) =>
        prisma.galleryMedia.create({
          data: {
            galleryId: id,
            fileName,
            filePath,
            fileType,
            caption
          }
        })
      )
    );

    res.status(201).json({
      message: 'Médias ajoutés avec succès',
      media: createdMedia
    });

  } catch (error) {
    console.error('Erreur lors de l\'ajout des médias:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// DELETE /api/galleries/:id/media/:mediaId - Supprimer un média (Admin seulement)
router.delete('/:id/media/:mediaId', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id, mediaId } = req.params;

    // Vérifier que le média existe et appartient à la galerie
    const media = await prisma.galleryMedia.findFirst({
      where: {
        id: mediaId,
        galleryId: id
      }
    });

    if (!media) {
      return res.status(404).json({
        message: 'Média non trouvé'
      });
    }

    await prisma.galleryMedia.delete({
      where: { id: mediaId }
    });

    res.json({
      message: 'Média supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression du média:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// DELETE /api/galleries/:id - Supprimer une galerie (Admin seulement)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que la galerie existe
    const existingGallery = await prisma.gallery.findUnique({
      where: { id }
    });

    if (!existingGallery) {
      return res.status(404).json({
        message: 'Galerie non trouvée'
      });
    }

    await prisma.gallery.delete({
      where: { id }
    });

    res.json({
      message: 'Galerie supprimée avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de la galerie:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
