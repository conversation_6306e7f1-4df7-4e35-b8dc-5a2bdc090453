import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Download,
  Eye,
  Check,
  X,
  Calendar,
  User,
  Mail,
  Phone,
  CreditCard,
  Ticket
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface Reservation {
  id: string;
  reservationNumber: string;
  customer: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  match: {
    homeTeam: string;
    awayTeam: string;
    matchDate: string;
    location: string;
  };
  tickets: {
    type: string;
    quantity: number;
    price: number;
  }[];
  totalAmount: number;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'REFUNDED';
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';
  createdAt: string;
  confirmedAt?: string;
}

const ReservationsManagement: React.FC = () => {
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [paymentFilter, setPaymentFilter] = useState<string>('ALL');
  const [selectedReservation, setSelectedReservation] = useState<Reservation | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - Replace with API call
  useEffect(() => {
    const mockReservations: Reservation[] = [
      {
        id: '1',
        reservationNumber: 'ESC-001234',
        customer: {
          firstName: 'Ahmed',
          lastName: 'Ben Ali',
          email: '<EMAIL>',
          phone: '+216 20 123 456'
        },
        match: {
          homeTeam: 'Espoir Sportif de Chorbane',
          awayTeam: 'AS Soliman',
          matchDate: '2025-05-28T15:00:00',
          location: 'Stade Municipal Chorbane'
        },
        tickets: [
          { type: 'Tribune Standard', quantity: 2, price: 15 },
          { type: 'Tribune VIP', quantity: 1, price: 35 }
        ],
        totalAmount: 65,
        status: 'CONFIRMED',
        paymentStatus: 'PAID',
        createdAt: '2025-05-20T10:30:00',
        confirmedAt: '2025-05-20T10:35:00'
      },
      {
        id: '2',
        reservationNumber: 'ESC-001235',
        customer: {
          firstName: 'Fatma',
          lastName: 'Trabelsi',
          email: '<EMAIL>',
          phone: '+216 25 987 654'
        },
        match: {
          homeTeam: 'Espoir Sportif de Chorbane',
          awayTeam: 'Club Africain',
          matchDate: '2025-06-05T18:00:00',
          location: 'Stade Municipal Chorbane'
        },
        tickets: [
          { type: 'Pack Famille', quantity: 1, price: 40 }
        ],
        totalAmount: 40,
        status: 'PENDING',
        paymentStatus: 'PENDING',
        createdAt: '2025-05-21T14:15:00'
      },
      {
        id: '3',
        reservationNumber: 'ESC-001236',
        customer: {
          firstName: 'Mohamed',
          lastName: 'Gharbi',
          email: '<EMAIL>',
          phone: '+216 22 456 789'
        },
        match: {
          homeTeam: 'Espoir Sportif de Chorbane',
          awayTeam: 'AS Soliman',
          matchDate: '2025-05-28T15:00:00',
          location: 'Stade Municipal Chorbane'
        },
        tickets: [
          { type: 'Tribune Standard', quantity: 4, price: 15 }
        ],
        totalAmount: 60,
        status: 'CANCELLED',
        paymentStatus: 'REFUNDED',
        createdAt: '2025-05-19T16:45:00'
      }
    ];
    setReservations(mockReservations);
    setLoading(false);
  }, []);

  const filteredReservations = reservations.filter(reservation => {
    const matchesSearch = 
      reservation.reservationNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.customer.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.customer.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.match.awayTeam.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'ALL' || reservation.status === statusFilter;
    const matchesPayment = paymentFilter === 'ALL' || reservation.paymentStatus === paymentFilter;
    
    return matchesSearch && matchesStatus && matchesPayment;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      PENDING: { color: 'bg-yellow-100 text-yellow-800', text: 'En attente' },
      CONFIRMED: { color: 'bg-green-100 text-green-800', text: 'Confirmée' },
      CANCELLED: { color: 'bg-red-100 text-red-800', text: 'Annulée' },
      REFUNDED: { color: 'bg-gray-100 text-gray-800', text: 'Remboursée' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getPaymentBadge = (status: string) => {
    const statusConfig = {
      PENDING: { color: 'bg-yellow-100 text-yellow-800', text: 'En attente' },
      PAID: { color: 'bg-green-100 text-green-800', text: 'Payé' },
      FAILED: { color: 'bg-red-100 text-red-800', text: 'Échec' },
      REFUNDED: { color: 'bg-gray-100 text-gray-800', text: 'Remboursé' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const handleConfirmReservation = (reservationId: string) => {
    setReservations(reservations.map(reservation => 
      reservation.id === reservationId 
        ? { 
            ...reservation, 
            status: 'CONFIRMED' as const, 
            paymentStatus: 'PAID' as const,
            confirmedAt: new Date().toISOString() 
          }
        : reservation
    ));
  };

  const handleCancelReservation = (reservationId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir annuler cette réservation ?')) {
      setReservations(reservations.map(reservation => 
        reservation.id === reservationId 
          ? { 
              ...reservation, 
              status: 'CANCELLED' as const,
              paymentStatus: 'REFUNDED' as const
            }
          : reservation
      ));
    }
  };

  const getTotalTickets = (tickets: Reservation['tickets']) => {
    return tickets.reduce((total, ticket) => total + ticket.quantity, 0);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des réservations</h1>
            <p className="mt-1 text-sm text-gray-600">
              Gérez les réservations de billets pour les matchs d'Espoir Sportif de Chorbane
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button className="btn btn-outline mr-3">
              <Download className="h-5 w-5 mr-2" />
              Exporter
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Ticket className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{reservations.length}</p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Check className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Confirmées</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reservations.filter(r => r.status === 'CONFIRMED').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Calendar className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">En attente</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reservations.filter(r => r.status === 'PENDING').length}
                </p>
              </div>
            </div>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <CreditCard className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Revenus</p>
                <p className="text-2xl font-bold text-gray-900">
                  {reservations
                    .filter(r => r.paymentStatus === 'PAID')
                    .reduce((total, r) => total + r.totalAmount, 0)} DT
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Rechercher une réservation..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="input pl-10"
              >
                <option value="ALL">Tous les statuts</option>
                <option value="PENDING">En attente</option>
                <option value="CONFIRMED">Confirmées</option>
                <option value="CANCELLED">Annulées</option>
                <option value="REFUNDED">Remboursées</option>
              </select>
            </div>
            <div>
              <select
                value={paymentFilter}
                onChange={(e) => setPaymentFilter(e.target.value)}
                className="input"
              >
                <option value="ALL">Tous les paiements</option>
                <option value="PENDING">En attente</option>
                <option value="PAID">Payés</option>
                <option value="FAILED">Échecs</option>
                <option value="REFUNDED">Remboursés</option>
              </select>
            </div>
            <div className="text-sm text-gray-600 flex items-center">
              <Ticket className="h-4 w-4 mr-2" />
              {filteredReservations.length} réservation{filteredReservations.length > 1 ? 's' : ''} trouvée{filteredReservations.length > 1 ? 's' : ''}
            </div>
          </div>
        </div>

        {/* Reservations List */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Réservation
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Client
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Match
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Billets
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Paiement
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredReservations.map((reservation) => (
                  <tr key={reservation.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {reservation.reservationNumber}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(reservation.createdAt)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {reservation.customer.firstName} {reservation.customer.lastName}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Mail className="h-3 w-3 mr-1" />
                          {reservation.customer.email}
                        </div>
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          {reservation.customer.phone}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {reservation.match.homeTeam} vs {reservation.match.awayTeam}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatDate(reservation.match.matchDate)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {reservation.match.location}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {getTotalTickets(reservation.tickets)} billet{getTotalTickets(reservation.tickets) > 1 ? 's' : ''}
                      </div>
                      <div className="text-xs text-gray-500">
                        {reservation.tickets.map((ticket, index) => (
                          <div key={index}>
                            {ticket.quantity}× {ticket.type}
                          </div>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(reservation.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getPaymentBadge(reservation.paymentStatus)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {reservation.totalAmount} DT
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => setSelectedReservation(reservation)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        {reservation.status === 'PENDING' && (
                          <>
                            <button
                              onClick={() => handleConfirmReservation(reservation.id)}
                              className="text-green-600 hover:text-green-900"
                            >
                              <Check className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleCancelReservation(reservation.id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredReservations.length === 0 && (
          <div className="text-center py-12">
            <Ticket className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucune réservation trouvée</h3>
            <p className="mt-1 text-sm text-gray-500">
              Aucune réservation ne correspond à vos critères de recherche.
            </p>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default ReservationsManagement;
