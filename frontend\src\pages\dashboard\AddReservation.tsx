import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Save,
  ArrowLeft,
  User,
  Mail,
  Phone,
  Calendar,
  Ticket,
  CreditCard,
  MapPin,
  Users
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface ReservationFormData {
  // Customer Info
  customerFirstName: string;
  customerLastName: string;
  customerEmail: string;
  customerPhone: string;
  
  // Match Selection
  selectedMatch: string;
  
  // Tickets
  tickets: {
    type: string;
    quantity: number;
    price: number;
  }[];
  
  // Payment
  paymentMethod: string;
  notes: string;
}

const AddReservation: React.FC = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState<ReservationFormData>({
    customerFirstName: '',
    customerLastName: '',
    customerEmail: '',
    customerPhone: '',
    selectedMatch: '',
    tickets: [
      { type: 'Tribune Standard', quantity: 1, price: 15 }
    ],
    paymentMethod: 'CASH',
    notes: ''
  });

  const [saving, setSaving] = useState(false);

  // Mock matches data
  const availableMatches = [
    {
      id: '1',
      homeTeam: 'Espoir Sportif de Chorbane',
      awayTeam: 'AS Soliman',
      date: '2025-05-28T15:00:00',
      location: 'Stade Municipal Chorbane',
      availableTickets: 250
    },
    {
      id: '2',
      homeTeam: 'Espoir Sportif de Chorbane',
      awayTeam: 'Club Africain',
      date: '2025-06-05T18:00:00',
      location: 'Stade Municipal Chorbane',
      availableTickets: 180
    }
  ];

  const ticketTypes = [
    { type: 'Tribune Standard', price: 15 },
    { type: 'Tribune VIP', price: 35 },
    { type: 'Pack Famille', price: 40 },
    { type: 'Étudiant', price: 10 }
  ];

  const paymentMethods = [
    { value: 'CASH', label: 'Espèces' },
    { value: 'CARD', label: 'Carte bancaire' },
    { value: 'TRANSFER', label: 'Virement' },
    { value: 'MOBILE', label: 'Paiement mobile' }
  ];

  const handleChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleTicketChange = (index: number, field: string, value: any) => {
    const newTickets = [...formData.tickets];
    newTickets[index] = { ...newTickets[index], [field]: value };
    
    // Update price when type changes
    if (field === 'type') {
      const ticketType = ticketTypes.find(t => t.type === value);
      if (ticketType) {
        newTickets[index].price = ticketType.price;
      }
    }
    
    setFormData(prev => ({ ...prev, tickets: newTickets }));
  };

  const addTicketType = () => {
    setFormData(prev => ({
      ...prev,
      tickets: [...prev.tickets, { type: 'Tribune Standard', quantity: 1, price: 15 }]
    }));
  };

  const removeTicketType = (index: number) => {
    setFormData(prev => ({
      ...prev,
      tickets: prev.tickets.filter((_, i) => i !== index)
    }));
  };

  const calculateTotal = () => {
    return formData.tickets.reduce((total, ticket) => total + (ticket.quantity * ticket.price), 0);
  };

  const getTotalTickets = () => {
    return formData.tickets.reduce((total, ticket) => total + ticket.quantity, 0);
  };

  const getSelectedMatch = () => {
    return availableMatches.find(match => match.id === formData.selectedMatch);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Generate reservation number
      const reservationNumber = `ESC-${Date.now().toString().slice(-6)}`;
      
      const reservationData = {
        ...formData,
        reservationNumber,
        totalAmount: calculateTotal(),
        status: 'PENDING',
        paymentStatus: 'PENDING',
        createdAt: new Date().toISOString()
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert(`Réservation créée avec succès !\nNuméro de réservation : ${reservationNumber}`);
      navigate('/dashboard/reservations');
    } catch (error) {
      alert('Erreur lors de la création de la réservation');
    } finally {
      setSaving(false);
    }
  };

  const formatMatchDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => navigate('/dashboard/reservations')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Nouvelle réservation</h1>
              <p className="mt-1 text-sm text-gray-600">
                Créez une nouvelle réservation de billets
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Customer Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Informations client</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prénom *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={formData.customerFirstName}
                    onChange={(e) => handleChange('customerFirstName', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={formData.customerLastName}
                    onChange={(e) => handleChange('customerLastName', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="email"
                    value={formData.customerEmail}
                    onChange={(e) => handleChange('customerEmail', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone *
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="tel"
                    value={formData.customerPhone}
                    onChange={(e) => handleChange('customerPhone', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Match Selection */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Sélection du match</h3>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Match *
              </label>
              <select
                value={formData.selectedMatch}
                onChange={(e) => handleChange('selectedMatch', e.target.value)}
                className="input"
                required
              >
                <option value="">Sélectionnez un match</option>
                {availableMatches.map(match => (
                  <option key={match.id} value={match.id}>
                    {match.homeTeam} vs {match.awayTeam} - {formatMatchDate(match.date)}
                  </option>
                ))}
              </select>
            </div>

            {/* Match Details */}
            {getSelectedMatch() && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-green-800">
                      {getSelectedMatch()?.homeTeam} vs {getSelectedMatch()?.awayTeam}
                    </h4>
                    <p className="text-sm text-green-600">
                      <Calendar className="inline h-4 w-4 mr-1" />
                      {formatMatchDate(getSelectedMatch()?.date || '')}
                    </p>
                    <p className="text-sm text-green-600">
                      <MapPin className="inline h-4 w-4 mr-1" />
                      {getSelectedMatch()?.location}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-green-600">
                      {getSelectedMatch()?.availableTickets} billets disponibles
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Tickets */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Billets</h3>
              <button
                type="button"
                onClick={addTicketType}
                className="btn btn-outline btn-sm"
              >
                Ajouter un type
              </button>
            </div>

            <div className="space-y-4">
              {formData.tickets.map((ticket, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 border rounded-lg">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type de billet
                    </label>
                    <select
                      value={ticket.type}
                      onChange={(e) => handleTicketChange(index, 'type', e.target.value)}
                      className="input"
                    >
                      {ticketTypes.map(type => (
                        <option key={type.type} value={type.type}>
                          {type.type}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Quantité
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={ticket.quantity}
                      onChange={(e) => handleTicketChange(index, 'quantity', parseInt(e.target.value))}
                      className="input"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Prix unitaire (DT)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.5"
                      value={ticket.price}
                      onChange={(e) => handleTicketChange(index, 'price', parseFloat(e.target.value))}
                      className="input"
                    />
                  </div>

                  <div className="flex items-end">
                    <div className="w-full">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Sous-total
                      </label>
                      <div className="input bg-gray-50 font-semibold">
                        {(ticket.quantity * ticket.price).toFixed(2)} DT
                      </div>
                    </div>
                    {formData.tickets.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeTicketType(index)}
                        className="ml-2 p-2 text-red-600 hover:text-red-800"
                      >
                        ×
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Total */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="flex justify-between items-center">
                <div>
                  <p className="text-sm text-blue-600">
                    <Ticket className="inline h-4 w-4 mr-1" />
                    Total : {getTotalTickets()} billet{getTotalTickets() > 1 ? 's' : ''}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-blue-800">
                    {calculateTotal().toFixed(2)} DT
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Payment */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Paiement</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Mode de paiement
                </label>
                <div className="relative">
                  <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <select
                    value={formData.paymentMethod}
                    onChange={(e) => handleChange('paymentMethod', e.target.value)}
                    className="input pl-10"
                  >
                    {paymentMethods.map(method => (
                      <option key={method.value} value={method.value}>
                        {method.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => handleChange('notes', e.target.value)}
                  rows={3}
                  className="input"
                  placeholder="Notes supplémentaires..."
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pb-8">
            <button
              type="button"
              onClick={() => navigate('/dashboard/reservations')}
              className="btn btn-outline"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving || !formData.selectedMatch}
              className="btn btn-primary"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Créer la réservation
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default AddReservation;
