import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Save,
  ArrowLeft,
  Upload,
  User,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Hash,
  Trophy,
  Activity
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface PlayerFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  nationality: string;
  position: string;
  jerseyNumber: number;
  height: number;
  weight: number;
  preferredFoot: string;
  contractStart: string;
  contractEnd: string;
  salary: number;
  photo?: string;
  biography: string;
  previousClubs: string;
  achievements: string;
}

const PlayerForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState<PlayerFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    nationality: 'Tunisie',
    position: 'MIDFIELDER',
    jerseyNumber: 1,
    height: 175,
    weight: 70,
    preferredFoot: 'RIGHT',
    contractStart: '',
    contractEnd: '',
    salary: 0,
    biography: '',
    previousClubs: '',
    achievements: ''
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  const positions = [
    { value: 'GOALKEEPER', label: 'Gardien de but' },
    { value: 'DEFENDER', label: 'Défenseur' },
    { value: 'MIDFIELDER', label: 'Milieu de terrain' },
    { value: 'FORWARD', label: 'Attaquant' }
  ];

  const preferredFootOptions = [
    { value: 'RIGHT', label: 'Droit' },
    { value: 'LEFT', label: 'Gauche' },
    { value: 'BOTH', label: 'Ambidextre' }
  ];

  useEffect(() => {
    if (isEditing) {
      // Simulate loading player data
      setLoading(true);
      setTimeout(() => {
        // Mock data for editing
        setFormData({
          firstName: 'Hamza',
          lastName: 'Bedoui',
          email: '<EMAIL>',
          phone: '+216 20 123 456',
          dateOfBirth: '1995-03-15',
          nationality: 'Tunisie',
          position: 'FORWARD',
          jerseyNumber: 10,
          height: 180,
          weight: 75,
          preferredFoot: 'RIGHT',
          contractStart: '2024-01-01',
          contractEnd: '2026-12-31',
          salary: 5000,
          biography: 'Attaquant talentueux avec une excellente vision du jeu.',
          previousClubs: 'ES Tunis, CA Bizertin',
          achievements: 'Meilleur buteur 2023, Sélection nationale U23'
        });
        setLoading(false);
      }, 1000);
    }
  }, [isEditing]);

  const handleChange = (field: keyof PlayerFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert(isEditing ? 'Joueur modifié avec succès !' : 'Joueur créé avec succès !');
      navigate('/dashboard/players');
    } catch (error) {
      alert('Erreur lors de la sauvegarde');
    } finally {
      setSaving(false);
    }
  };

  const calculateAge = (birthDate: string) => {
    if (!birthDate) return '';
    const today = new Date();
    const birth = new Date(birthDate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return age - 1;
    }
    return age;
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => navigate('/dashboard/players')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {isEditing ? 'Modifier le joueur' : 'Nouveau joueur'}
              </h1>
              <p className="mt-1 text-sm text-gray-600">
                {isEditing ? 'Modifiez les informations du joueur' : 'Ajoutez un nouveau joueur à l\'équipe'}
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Photo Section */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Photo du joueur</h3>
            <div className="flex items-center space-x-6">
              <div className="flex-shrink-0">
                <div className="w-32 h-32 bg-gray-200 rounded-lg flex items-center justify-center">
                  {formData.photo ? (
                    <img
                      src={formData.photo}
                      alt="Photo du joueur"
                      className="w-32 h-32 rounded-lg object-cover"
                    />
                  ) : (
                    <User className="h-16 w-16 text-gray-400" />
                  )}
                </div>
              </div>
              <div>
                <button
                  type="button"
                  className="btn btn-outline"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Télécharger une photo
                </button>
                <p className="mt-2 text-sm text-gray-500">
                  JPG, PNG jusqu'à 2MB. Recommandé : 400x400px
                </p>
              </div>
            </div>
          </div>

          {/* Personal Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Informations personnelles</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prénom *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => handleChange('firstName', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nom *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => handleChange('lastName', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email *
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleChange('email', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Téléphone
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => handleChange('phone', e.target.value)}
                    className="input pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date de naissance *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="date"
                    value={formData.dateOfBirth}
                    onChange={(e) => handleChange('dateOfBirth', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
                {formData.dateOfBirth && (
                  <p className="mt-1 text-sm text-gray-500">
                    Âge : {calculateAge(formData.dateOfBirth)} ans
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Nationalité
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    value={formData.nationality}
                    onChange={(e) => handleChange('nationality', e.target.value)}
                    className="input pl-10"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Football Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Informations footballistiques</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Position *
                </label>
                <select
                  value={formData.position}
                  onChange={(e) => handleChange('position', e.target.value)}
                  className="input"
                  required
                >
                  {positions.map(position => (
                    <option key={position.value} value={position.value}>
                      {position.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Numéro de maillot *
                </label>
                <div className="relative">
                  <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="number"
                    min="1"
                    max="99"
                    value={formData.jerseyNumber}
                    onChange={(e) => handleChange('jerseyNumber', parseInt(e.target.value))}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Pied préféré
                </label>
                <select
                  value={formData.preferredFoot}
                  onChange={(e) => handleChange('preferredFoot', e.target.value)}
                  className="input"
                >
                  {preferredFootOptions.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Taille (cm)
                </label>
                <div className="relative">
                  <Activity className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="number"
                    min="150"
                    max="220"
                    value={formData.height}
                    onChange={(e) => handleChange('height', parseInt(e.target.value))}
                    className="input pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Poids (kg)
                </label>
                <div className="relative">
                  <Activity className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="number"
                    min="50"
                    max="120"
                    value={formData.weight}
                    onChange={(e) => handleChange('weight', parseInt(e.target.value))}
                    className="input pl-10"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Contract Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Informations contractuelles</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Début de contrat
                </label>
                <input
                  type="date"
                  value={formData.contractStart}
                  onChange={(e) => handleChange('contractStart', e.target.value)}
                  className="input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Fin de contrat
                </label>
                <input
                  type="date"
                  value={formData.contractEnd}
                  onChange={(e) => handleChange('contractEnd', e.target.value)}
                  className="input"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Salaire mensuel (DT)
                </label>
                <input
                  type="number"
                  min="0"
                  step="100"
                  value={formData.salary}
                  onChange={(e) => handleChange('salary', parseFloat(e.target.value))}
                  className="input"
                />
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Informations supplémentaires</h3>
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Biographie
                </label>
                <textarea
                  value={formData.biography}
                  onChange={(e) => handleChange('biography', e.target.value)}
                  rows={3}
                  className="input"
                  placeholder="Décrivez le parcours et les qualités du joueur..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Clubs précédents
                </label>
                <input
                  type="text"
                  value={formData.previousClubs}
                  onChange={(e) => handleChange('previousClubs', e.target.value)}
                  className="input"
                  placeholder="Ex: ES Tunis (2020-2022), CA Bizertin (2022-2024)"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Réalisations et distinctions
                </label>
                <textarea
                  value={formData.achievements}
                  onChange={(e) => handleChange('achievements', e.target.value)}
                  rows={2}
                  className="input"
                  placeholder="Ex: Meilleur buteur 2023, Sélection nationale U23..."
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pb-8">
            <button
              type="button"
              onClick={() => navigate('/dashboard/players')}
              className="btn btn-outline"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sauvegarde...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Modifier' : 'Créer'} le joueur
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default PlayerForm;
