import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Users, Trophy, ArrowRight, Play } from 'lucide-react';

const HomePage = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background */}
        <div className="absolute inset-0 gradient-hero"></div>
        <div className="absolute inset-0 bg-hero-pattern opacity-10"></div>

        {/* Content */}
        <div className="relative z-10 container-custom text-center text-white">
          <div className="animate-fade-in-up">
            <h1 className="hero-title mb-6">
              Espoir Sportif de Chorbane

            </h1>
            <p className="hero-subtitle mb-8 mx-auto text-gray-200">
              Passion, Excellence et Fierté Tunisienne. Suivez notre équipe dans sa quête de victoires
              et découvrez l'esprit sportif qui nous anime depuis des générations.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/matches" className="btn btn-secondary btn-lg">
                <Calendar className="w-5 h-5 mr-2" />
                Voir les Matchs
              </Link>
              <Link to="/players" className="btn btn-outline btn-lg border-white text-white hover:bg-white hover:text-primary-600">
                <Users className="w-5 h-5 mr-2" />
                Notre Équipe
              </Link>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce-subtle">
          <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="stat-number">25+</div>
              <div className="stat-label">Matchs Joués</div>
            </div>
            <div className="text-center">
              <div className="stat-number">29</div>
              <div className="stat-label">Joueurs</div>
            </div>
            <div className="text-center">
              <div className="stat-number">15</div>
              <div className="stat-label">Victoires</div>
            </div>
            <div className="text-center">
              <div className="stat-number">1950</div>
              <div className="stat-label">Fondé en</div>
            </div>
          </div>
        </div>
      </section>

      {/* Next Match Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-dark-900 mb-4">
              Prochain Match
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Ne manquez pas notre prochain affrontement à domicile
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="card card-hover p-8">
              <div className="flex flex-col md:flex-row items-center justify-between">
                <div className="flex items-center space-x-6 mb-6 md:mb-0">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-2">
                      <span className="text-white font-bold text-xl">ESC</span>
                    </div>
                    <p className="font-semibold text-dark-900">Espoir Sportif</p>
                    <p className="text-sm text-gray-600">de Chorbane</p>
                  </div>

                  <div className="text-center">
                    <div className="text-2xl font-bold text-dark-900 mb-2">VS</div>
                    <div className="text-sm text-gray-600">28 Mai 2025</div>
                    <div className="text-sm text-gray-600">15:00</div>
                  </div>

                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-300 rounded-full flex items-center justify-center mb-2">
                      <span className="text-gray-600 font-bold text-xl">AS</span>
                    </div>
                    <p className="font-semibold text-dark-900">AS Soliman</p>
                    <p className="text-sm text-gray-600">Soliman</p>
                  </div>
                </div>

                <div className="text-center md:text-right">
                  <p className="text-sm text-gray-600 mb-2">Stade Municipal</p>
                  <p className="text-sm text-gray-600 mb-4">Chorbane</p>
                  <button className="btn btn-primary btn-md">
                    <Trophy className="w-4 h-4 mr-2" />
                    Réserver
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Latest News Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-dark-900 mb-4">
                Dernières Actualités
              </h2>
              <p className="text-gray-600">
                Restez informé de toute l'actualité du club
              </p>
            </div>
            <Link to="/news" className="btn btn-outline btn-md hidden md:flex">
              Voir tout
              <ArrowRight className="w-4 h-4 ml-2" />
            </Link>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((item) => (
              <article key={item} className="card card-hover">
                <div className="aspect-video bg-gradient-to-br from-primary-400 to-primary-600 relative overflow-hidden">
                  <div className="absolute inset-0 bg-black bg-opacity-20 flex items-center justify-center">
                    <Play className="w-12 h-12 text-white opacity-80" />
                  </div>
                </div>
                <div className="p-6">
                  <div className="text-sm text-primary-600 font-medium mb-2">
                    {item === 1 ? 'Match' : item === 2 ? 'Transfert' : 'Entraînement'}
                  </div>
                  <h3 className="text-xl font-semibold text-dark-900 mb-3 line-clamp-2">
                    {item === 1
                      ? 'Victoire éclatante contre le CA Bizertin'
                      : item === 2
                      ? 'Nouveau renfort : Arrivée de Montassar Meriah'
                      : 'Préparation intensive pour la nouvelle saison'
                    }
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit.
                    Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
                  </p>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Il y a 2 jours</span>
                    <Link to="/news" className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                      Lire plus →
                    </Link>
                  </div>
                </div>
              </article>
            ))}
          </div>

          <div className="text-center mt-8 md:hidden">
            <Link to="/news" className="btn btn-outline btn-md">
              Voir toutes les actualités
              <ArrowRight className="w-4 h-4 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
