const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../config/database');
const { authenticateToken, requireAdmin, requirePlayerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /api/trainings - Récupérer tous les entraînements
router.get('/', requirePlayerOrAdmin, async (req, res) => {
  try {
    const { page = 1, limit = 10, upcoming = false, past = false } = req.query;
    const skip = (page - 1) * limit;

    // Construire les filtres
    const where = {};
    if (upcoming === 'true') {
      where.date = {
        gte: new Date()
      };
    }
    if (past === 'true') {
      where.date = {
        lt: new Date()
      };
    }

    const [trainings, total] = await Promise.all([
      prisma.training.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          attendances: {
            include: {
              player: {
                include: {
                  user: {
                    select: {
                      firstName: true,
                      lastName: true
                    }
                  }
                }
              }
            }
          }
        },
        orderBy: {
          date: 'desc'
        }
      }),
      prisma.training.count({ where })
    ]);

    res.json({
      message: 'Entraînements récupérés avec succès',
      trainings,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des entraînements:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/trainings/:id - Récupérer un entraînement par ID
router.get('/:id', requirePlayerOrAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const training = await prisma.training.findUnique({
      where: { id },
      include: {
        attendances: {
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    avatar: true
                  }
                }
              }
            }
          },
          orderBy: {
            player: {
              jerseyNumber: 'asc'
            }
          }
        }
      }
    });

    if (!training) {
      return res.status(404).json({
        message: 'Entraînement non trouvé'
      });
    }

    res.json({
      message: 'Entraînement récupéré avec succès',
      training
    });

  } catch (error) {
    console.error('Erreur lors de la récupération de l\'entraînement:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/trainings - Créer un nouvel entraînement (Admin seulement)
router.post('/', [
  body('title')
    .notEmpty()
    .withMessage('Titre de l\'entraînement requis'),
  body('date')
    .isISO8601()
    .withMessage('Date d\'entraînement invalide'),
  body('location')
    .notEmpty()
    .withMessage('Lieu de l\'entraînement requis'),
  body('duration')
    .isInt({ min: 15, max: 300 })
    .withMessage('Durée invalide (15-300 minutes)')
], requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { title, description, date, location, duration } = req.body;

    const training = await prisma.training.create({
      data: {
        title,
        description,
        date: new Date(date),
        location,
        duration
      }
    });

    // Créer automatiquement les présences pour tous les joueurs actifs
    const activePlayers = await prisma.player.findMany({
      where: { isActive: true }
    });

    if (activePlayers.length > 0) {
      await prisma.trainingAttendance.createMany({
        data: activePlayers.map(player => ({
          trainingId: training.id,
          playerId: player.id,
          isPresent: false
        }))
      });
    }

    const trainingWithAttendances = await prisma.training.findUnique({
      where: { id: training.id },
      include: {
        attendances: {
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        }
      }
    });

    res.status(201).json({
      message: 'Entraînement créé avec succès',
      training: trainingWithAttendances
    });

  } catch (error) {
    console.error('Erreur lors de la création de l\'entraînement:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// PUT /api/trainings/:id - Mettre à jour un entraînement (Admin seulement)
router.put('/:id', [
  body('title')
    .optional()
    .notEmpty()
    .withMessage('Titre de l\'entraînement requis'),
  body('date')
    .optional()
    .isISO8601()
    .withMessage('Date d\'entraînement invalide'),
  body('location')
    .optional()
    .notEmpty()
    .withMessage('Lieu de l\'entraînement requis'),
  body('duration')
    .optional()
    .isInt({ min: 15, max: 300 })
    .withMessage('Durée invalide (15-300 minutes)')
], requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { title, description, date, location, duration } = req.body;

    // Vérifier que l'entraînement existe
    const existingTraining = await prisma.training.findUnique({
      where: { id }
    });

    if (!existingTraining) {
      return res.status(404).json({
        message: 'Entraînement non trouvé'
      });
    }

    const updatedTraining = await prisma.training.update({
      where: { id },
      data: {
        title,
        description,
        date: date ? new Date(date) : undefined,
        location,
        duration
      },
      include: {
        attendances: {
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        }
      }
    });

    res.json({
      message: 'Entraînement mis à jour avec succès',
      training: updatedTraining
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'entraînement:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// PUT /api/trainings/:id/attendance - Mettre à jour les présences (Admin seulement)
router.put('/:id/attendance', [
  body('attendances')
    .isArray()
    .withMessage('Liste des présences requise')
], requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { attendances } = req.body;

    // Vérifier que l'entraînement existe
    const training = await prisma.training.findUnique({
      where: { id }
    });

    if (!training) {
      return res.status(404).json({
        message: 'Entraînement non trouvé'
      });
    }

    // Mettre à jour les présences
    await Promise.all(
      attendances.map(({ playerId, isPresent, notes }) =>
        prisma.trainingAttendance.upsert({
          where: {
            trainingId_playerId: {
              trainingId: id,
              playerId
            }
          },
          update: {
            isPresent,
            notes
          },
          create: {
            trainingId: id,
            playerId,
            isPresent,
            notes
          }
        })
      )
    );

    const updatedTraining = await prisma.training.findUnique({
      where: { id },
      include: {
        attendances: {
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        }
      }
    });

    res.json({
      message: 'Présences mises à jour avec succès',
      training: updatedTraining
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour des présences:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// DELETE /api/trainings/:id - Supprimer un entraînement (Admin seulement)
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que l'entraînement existe
    const existingTraining = await prisma.training.findUnique({
      where: { id }
    });

    if (!existingTraining) {
      return res.status(404).json({
        message: 'Entraînement non trouvé'
      });
    }

    await prisma.training.delete({
      where: { id }
    });

    res.json({
      message: 'Entraînement supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de l\'entraînement:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
