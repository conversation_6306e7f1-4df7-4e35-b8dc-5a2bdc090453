# Documentation Faker - Génération de Données de Test

## 📋 Vue d'ensemble

Ce document explique comment utiliser Faker.js pour générer des données de test réalistes pour l'application Espoir Sportive Chorbane.

## 🛠️ Configuration Faker

### Installation
```bash
cd backend
npm install @faker-js/faker --save-dev
```

### Configuration
- **Locale** : Français (`fr`)
- **Données spécifiques** : Noms tunisiens, clubs de football, stades
- **Génération réaliste** : Statistiques de football, scores, événements

## 📁 Structure des Fichiers

### `faker-helpers.js`
Utilitaires et fonctions spécialisées pour générer des données réalistes :
- Noms tunisiens authentiques
- Emails réalistes
- Numéros de téléphone tunisiens
- Statistiques de football par position
- Scores de match réalistes

### `seed-advanced.js`
Script de seed principal avec Faker pour générer un jeu de données complet :
- 28 joueurs (3 gardiens, 8 défenseurs, 10 milieux, 7 attaquants)
- 25 supporters
- 25 matchs avec scores réalistes
- 30 entraînements avec présences
- Statistiques sur plusieurs saisons

### `generate-data.js`
Script modulaire pour générer des données spécifiques :
- Articles de blog
- Matchs supplémentaires
- Supporters
- Commentaires
- Événements de match

## 🎯 Données Générées

### 👥 Utilisateurs et Joueurs

#### Noms Tunisiens Réalistes
```javascript
const tunisianFirstNames = [
  'Ahmed', 'Mohamed', 'Ali', 'Youssef', 'Karim', 'Sami', 'Fares', 'Hamza',
  'Amine', 'Bilel', 'Wassim', 'Nader', 'Maher', 'Rami', 'Saif', 'Zied'
  // ... plus de noms
];

const tunisianLastNames = [
  'Ben Ali', 'Trabelsi', 'Khedira', 'Bouazizi', 'Gharbi', 'Mejri', 'Bedoui',
  'Sassi', 'Ifa', 'Jaziri', 'Ghandri', 'Chammam', 'Msakni', 'Khazri'
  // ... plus de noms
];
```

#### Caractéristiques Physiques par Position
```javascript
// Gardien de but
height: 1.80-1.95m, weight: 75-90kg

// Défenseur
height: 1.75-1.90m, weight: 70-85kg

// Milieu de terrain
height: 1.65-1.80m, weight: 65-80kg

// Attaquant
height: 1.70-1.85m, weight: 68-82kg
```

#### Statistiques Réalistes par Position
```javascript
// Gardien : 0-1 buts, 0-2 passes décisives
// Défenseur : 0-4 buts, 0-6 passes décisives
// Milieu : 1-8 buts, 2-12 passes décisives
// Attaquant : 3-15 buts, 1-8 passes décisives
```

### ⚽ Matchs et Scores

#### Distribution Réaliste des Scores
```javascript
const scoreDistribution = [
  { home: 0, away: 0, weight: 8 },  // 0-0 (8% de chance)
  { home: 1, away: 0, weight: 15 }, // 1-0 (15% de chance)
  { home: 1, away: 1, weight: 12 }, // 1-1 (12% de chance)
  { home: 2, away: 1, weight: 8 },  // 2-1 (8% de chance)
  // ... distribution complète
];
```

#### Clubs Tunisiens Réels
```javascript
const footballClubs = [
  'ES Tunis', 'CA Bizertin', 'US Monastir', 'CS Sfaxien',
  'Club Africain', 'Stade Tunisien', 'AS Soliman', 'US Ben Guerdane'
  // ... plus de clubs
];
```

### 📰 Articles et Contenu

#### Types d'Articles
- **Résultats de match** : Victoires, défaites, matchs nuls
- **Recrutements** : Nouveaux joueurs
- **Entraînements** : Préparation, stages
- **Interviews** : Joueurs, staff
- **Actualités** : Événements du club

#### Génération de Titres Contextuels
```javascript
const articleTitles = {
  'match_result': [
    'Victoire éclatante contre {team} ({score})',
    'Match nul héroïque face à {team}',
    'Remontada spectaculaire contre {team}'
  ],
  'recruitment': [
    'Bienvenue à {player} !',
    'Nouveau renfort : {player} rejoint l\'équipe'
  ]
};
```

## 🚀 Utilisation

### Scripts Disponibles

#### Seed Complet
```bash
# Seed basique (données minimales)
npm run db:seed

# Seed avancé avec Faker (données réalistes)
npm run db:seed-advanced

# Reset complet avec seed avancé
npm run db:reset-advanced
```

#### Génération de Données Spécifiques
```bash
# Générer 10 articles
npm run db:generate-data articles 10

# Générer 5 matchs
npm run db:generate-data matches 5

# Générer 20 supporters
npm run db:generate-data supporters 20

# Générer des commentaires (max 5 par article)
npm run db:generate-data comments 5

# Générer des événements de match
npm run db:generate-data events

# Générer un peu de tout
npm run db:generate-data all
```

### Utilisation Programmatique

#### Générer un Joueur
```javascript
const { generateTunisianName, generatePlayerPhysicalStats } = require('./faker-helpers');

const { firstName, lastName } = generateTunisianName();
const physicalStats = generatePlayerPhysicalStats('MIDFIELDER');

console.log(`${firstName} ${lastName}`); // "Ahmed Ben Ali"
console.log(physicalStats); // { height: 1.72, weight: 71.5 }
```

#### Générer un Match
```javascript
const { generateRealisticMatchScore } = require('./faker-helpers');

const score = generateRealisticMatchScore();
console.log(`${score.homeScore}-${score.awayScore}`); // "2-1"
```

#### Générer un Article
```javascript
const { generateFootballArticleTitle } = require('./faker-helpers');

const title = generateFootballArticleTitle('match_result', {
  team: 'ES Tunis',
  score: '3-1'
});
console.log(title); // "Victoire éclatante contre ES Tunis (3-1)"
```

## 📊 Statistiques Générées

### Seed Avancé
- **28 joueurs** avec positions réalistes
- **25 supporters** avec profils variés
- **25 matchs** avec scores et dates réalistes
- **30 entraînements** avec présences (88% de taux de présence)
- **Statistiques** sur 3 saisons (2022-2023, 2023-2024, 2024-2025)

### Répartition des Joueurs
```
Gardiens de but : 3 joueurs (maillots 1-3)
Défenseurs : 8 joueurs (maillots 4-11)
Milieux de terrain : 10 joueurs (maillots 12-21)
Attaquants : 7 joueurs (maillots 22-28)
Joueur test : 1 joueur (maillot 99)
```

### Données Temporelles
- **Matchs** : Entre -4 mois et +2 mois
- **Entraînements** : Entre -3 mois et +1 mois
- **Dates de naissance** : Entre 1995 et 2006
- **Dates d'arrivée** : Entre 2019 et 2024

## 🔧 Personnalisation

### Ajouter de Nouveaux Noms
```javascript
// Dans faker-helpers.js
const tunisianFirstNames = [
  ...existingNames,
  'Nouveau', 'Nom', 'Ici'
];
```

### Modifier les Probabilités
```javascript
// Probabilité de présence aux entraînements
const isPresent = faker.datatype.boolean({ probability: 0.88 }); // 88%

// Probabilité d'article publié
const isPublished = faker.datatype.boolean({ probability: 0.85 }); // 85%
```

### Ajouter de Nouveaux Types d'Articles
```javascript
const articleTypes = [
  'match_result',
  'recruitment',
  'injury_report', // Nouveau type
  'transfer_news'  // Nouveau type
];
```

## 🧪 Tests et Validation

### Vérifier la Cohérence des Données
```bash
# Vérifier les statistiques générées
npm run db:studio

# Compter les joueurs par position
SELECT position, COUNT(*) FROM Player GROUP BY position;

# Vérifier les scores de match
SELECT homeScore, awayScore, COUNT(*) FROM Match 
WHERE status = 'FINISHED' 
GROUP BY homeScore, awayScore 
ORDER BY COUNT(*) DESC;
```

### Validation des Emails
```javascript
// Tous les emails générés sont uniques et valides
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
```

### Validation des Téléphones
```javascript
// Format tunisien : +216 XX XXX XXX
const phoneRegex = /^\+216 \d{2} \d{3} \d{3}$/;
```

## 🔄 Maintenance

### Mise à Jour des Données
```bash
# Ajouter plus de données sans reset
npm run db:generate-data all

# Reset complet avec nouvelles données
npm run db:reset-advanced
```

### Nettoyage
```bash
# Supprimer seulement les données de test
# (garder la structure)
npm run db:reset
```

## 📈 Performance

### Optimisations
- **Batch inserts** pour les grandes quantités de données
- **Transactions** pour maintenir la cohérence
- **Index** sur les champs recherchés fréquemment

### Temps de Génération
- **Seed basique** : ~10 secondes
- **Seed avancé** : ~30 secondes
- **Génération spécifique** : ~5 secondes

---

*Documentation Faker mise à jour le 24/05/2025*
