import React from 'react';
import { Users, MapPin, Calendar } from 'lucide-react';

const PlayersPage = () => {
  const players = [
    { id: 1, name: '<PERSON>', position: '<PERSON><PERSON><PERSON>', number: 1, age: 28, nationality: 'TUN' },
    { id: 2, name: '<PERSON>', position: '<PERSON><PERSON><PERSON><PERSON><PERSON>', number: 2, age: 25, nationality: 'TUN' },
    { id: 3, name: '<PERSON><PERSON><PERSON>', position: '<PERSON><PERSON><PERSON><PERSON><PERSON>', number: 3, age: 24, nationality: 'TUN' },
    { id: 4, name: '<PERSON><PERSON>', position: 'Milieu', number: 8, age: 26, nationality: 'TUN' },
    { id: 5, name: '<PERSON><PERSON>', position: 'Attaquant', number: 10, age: 23, nationality: 'TUN' },
    { id: 6, name: '<PERSON><PERSON><PERSON>', position: '<PERSON><PERSON><PERSON><PERSON><PERSON>', number: 5, age: 29, nationality: 'TUN' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="gradient-hero text-white section-padding">
        <div className="container-custom text-center">
          <h1 className="hero-title mb-6">Notre Équipe</h1>
          <p className="hero-subtitle mx-auto">
            Découvrez les joueurs talentueux qui portent fièrement les couleurs d'Espoir Sportif de Chorbane
          </p>
        </div>
      </section>

      {/* Players Grid */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {players.map((player) => (
              <div key={player.id} className="card card-hover">
                <div className="aspect-square bg-gradient-to-br from-primary-400 to-primary-600 relative overflow-hidden">
                  <div className="absolute top-4 left-4 bg-white bg-opacity-20 backdrop-blur-sm rounded-lg px-3 py-1">
                    <span className="text-white font-bold text-lg">#{player.number}</span>
                  </div>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <Users className="w-16 h-16 text-white opacity-50" />
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-dark-900 mb-2">{player.name}</h3>
                  <p className="text-primary-600 font-medium mb-3">{player.position}</p>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4" />
                      <span>{player.age} ans</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4" />
                      <span>{player.nationality}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default PlayersPage;
