# Configuration Docker Compose pour Espoir Sportive Chorbane

# Base de données PostgreSQL
POSTGRES_DB=espoir_sportive_db
POSTGRES_USER=espoir_user
POSTGRES_PASSWORD=espoir_password_secure_2024

# Backend API
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-minimum-32-characters
JWT_EXPIRES_IN=7d
NODE_ENV=production
BACKEND_PORT=5000

# Frontend
FRONTEND_PORT=3000
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=Espoir Sportive Chorbane
VITE_APP_VERSION=1.0.0

# Nginx
NGINX_HTTP_PORT=80
NGINX_HTTPS_PORT=443

# Redis (optionnel)
REDIS_PASSWORD=redis_password_secure_2024

# Adminer (développement)
ADMINER_PORT=8080

# Volumes
POSTGRES_DATA_PATH=./data/postgres
REDIS_DATA_PATH=./data/redis
UPLOADS_PATH=./data/uploads
LOGS_PATH=./data/logs

# SSL (production)
SSL_CERT_PATH=./nginx/ssl/cert.pem
SSL_KEY_PATH=./nginx/ssl/key.pem

# Monitoring (optionnel)
ENABLE_MONITORING=false
GRAFANA_PORT=3001
PROMETHEUS_PORT=9090
