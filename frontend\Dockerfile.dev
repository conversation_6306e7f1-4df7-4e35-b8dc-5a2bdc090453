# Dockerfile pour le développement avec hot reload
FROM node:18-alpine

# Installer les dépendances système nécessaires
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Créer un utilisateur non-root pour la sécurité
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Définir le répertoire de travail
WORKDIR /app

# Changer le propriétaire du répertoire
RUN chown -R nodejs:nodejs /app

# Copier les fichiers de dépendances
COPY package*.json ./

# Changer le propriétaire des fichiers copiés
RUN chown -R nodejs:nodejs /app

# Changer vers l'utilisateur nodejs pour l'installation
USER nodejs

# Installer toutes les dépendances (dev + prod)
RUN npm install

# Revenir à root pour ajuster les permissions finales
USER root
RUN chown -R nodejs:nodejs /app

# Changer vers l'utilisateur non-root
USER nodejs

# Exposer le port Vite par défaut
EXPOSE 3000

# Définir les variables d'environnement par défaut
ENV NODE_ENV=development

# Vérification de santé
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:3000 || exit 1

# Utiliser dumb-init pour gérer les signaux
ENTRYPOINT ["dumb-init", "--"]

# Commande par défaut pour le développement
CMD ["npm", "run", "dev", "--", "--host", "0.0.0.0"]
