import React, { useState, useEffect } from 'react';
import { X, Calendar, MapPin, Trophy, DollarSign } from 'lucide-react';

interface Match {
  id?: string;
  homeTeam: string;
  awayTeam: string;
  matchDate: string;
  location: string;
  status: 'SCHEDULED' | 'LIVE' | 'FINISHED' | 'CANCELLED';
  homeScore?: number;
  awayScore?: number;
  competition: string;
  ticketPrice?: number;
  availableTickets?: number;
}

interface MatchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (match: Match) => void;
  match?: Match | null;
}

const MatchModal: React.FC<MatchModalProps> = ({ isOpen, onClose, onSave, match }) => {
  const [formData, setFormData] = useState<Match>({
    homeTeam: 'Espoir Sportif de Chorbane',
    awayTeam: '',
    matchDate: '',
    location: 'Stade Municipal Chorbane',
    status: 'SCHEDULED',
    competition: 'Championnat National',
    ticketPrice: 15,
    availableTickets: 300
  });

  const [loading, setSaving] = useState(false);

  useEffect(() => {
    if (match) {
      setFormData(match);
    } else {
      setFormData({
        homeTeam: 'Espoir Sportif de Chorbane',
        awayTeam: '',
        matchDate: '',
        location: 'Stade Municipal Chorbane',
        status: 'SCHEDULED',
        competition: 'Championnat National',
        ticketPrice: 15,
        availableTickets: 300
      });
    }
  }, [match]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      onSave(formData);
      onClose();
    } catch (error) {
      alert('Erreur lors de la sauvegarde');
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (field: keyof Match, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <form onSubmit={handleSubmit}>
            <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">
                  {match ? 'Modifier le match' : 'Nouveau match'}
                </h3>
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Équipe à domicile
                  </label>
                  <input
                    type="text"
                    value={formData.homeTeam}
                    onChange={(e) => handleChange('homeTeam', e.target.value)}
                    className="input"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Équipe visiteur
                  </label>
                  <input
                    type="text"
                    value={formData.awayTeam}
                    onChange={(e) => handleChange('awayTeam', e.target.value)}
                    className="input"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date et heure
                  </label>
                  <div className="relative">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="datetime-local"
                      value={formData.matchDate}
                      onChange={(e) => handleChange('matchDate', e.target.value)}
                      className="input pl-10"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Lieu
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      value={formData.location}
                      onChange={(e) => handleChange('location', e.target.value)}
                      className="input pl-10"
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Compétition
                  </label>
                  <div className="relative">
                    <Trophy className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <select
                      value={formData.competition}
                      onChange={(e) => handleChange('competition', e.target.value)}
                      className="input pl-10"
                      required
                    >
                      <option value="Championnat National">Championnat National</option>
                      <option value="Coupe de Tunisie">Coupe de Tunisie</option>
                      <option value="Coupe de la CAF">Coupe de la CAF</option>
                      <option value="Match amical">Match amical</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Statut
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => handleChange('status', e.target.value as Match['status'])}
                    className="input"
                    required
                  >
                    <option value="SCHEDULED">Programmé</option>
                    <option value="LIVE">En cours</option>
                    <option value="FINISHED">Terminé</option>
                    <option value="CANCELLED">Annulé</option>
                  </select>
                </div>

                {formData.status === 'FINISHED' && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Score domicile
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={formData.homeScore || ''}
                        onChange={(e) => handleChange('homeScore', parseInt(e.target.value) || 0)}
                        className="input"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Score visiteur
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={formData.awayScore || ''}
                        onChange={(e) => handleChange('awayScore', parseInt(e.target.value) || 0)}
                        className="input"
                      />
                    </div>
                  </>
                )}

                {(formData.status === 'SCHEDULED' || formData.status === 'LIVE') && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Prix du billet (DT)
                      </label>
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                        <input
                          type="number"
                          min="0"
                          step="0.01"
                          value={formData.ticketPrice || ''}
                          onChange={(e) => handleChange('ticketPrice', parseFloat(e.target.value) || 0)}
                          className="input pl-10"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Billets disponibles
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={formData.availableTickets || ''}
                        onChange={(e) => handleChange('availableTickets', parseInt(e.target.value) || 0)}
                        className="input"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="submit"
                disabled={loading}
                className="btn btn-primary sm:ml-3"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Sauvegarde...
                  </div>
                ) : (
                  match ? 'Modifier' : 'Créer'
                )}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn btn-outline mt-3 sm:mt-0"
              >
                Annuler
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default MatchModal;
