import React, { useState, useEffect } from 'react';
import {
  Calendar,
  MapPin,
  Clock,
  Users,
  CreditCard,
  Ticket,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface Match {
  id: string;
  homeTeam: string;
  awayTeam: string;
  matchDate: string;
  location: string;
  ticketPrice: number;
  availableTickets: number;
  totalTickets: number;
  description?: string;
}

interface TicketType {
  id: string;
  name: string;
  price: number;
  description: string;
  available: number;
}

const ReservationPage: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const [matches, setMatches] = useState<Match[]>([]);
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null);
  const [ticketTypes] = useState<TicketType[]>([
    {
      id: 'standard',
      name: 'Billet Standard',
      price: 15,
      description: 'Accès aux tribunes générales',
      available: 150
    },
    {
      id: 'vip',
      name: 'Billet VIP',
      price: 35,
      description: 'Accès tribune VIP + rafraîchissements',
      available: 25
    },
    {
      id: 'family',
      name: 'Pack Famille',
      price: 40,
      description: '2 adultes + 2 enfants (-12 ans)',
      available: 30
    }
  ]);
  const [selectedTickets, setSelectedTickets] = useState<Record<string, number>>({});
  const [customerInfo, setCustomerInfo] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    email: user?.email || '',
    phone: user?.phone || ''
  });
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);

  // Mock matches data
  useEffect(() => {
    const mockMatches: Match[] = [
      {
        id: '1',
        homeTeam: 'Espoir Sportif de Chorbane',
        awayTeam: 'AS Soliman',
        matchDate: '2025-05-28T15:00:00',
        location: 'Stade Municipal Chorbane',
        ticketPrice: 15,
        availableTickets: 205,
        totalTickets: 300,
        description: 'Match de championnat - 28ème journée'
      },
      {
        id: '2',
        homeTeam: 'Espoir Sportif de Chorbane',
        awayTeam: 'CA Bizertin',
        matchDate: '2025-06-05T16:30:00',
        location: 'Stade Municipal Chorbane',
        ticketPrice: 20,
        availableTickets: 180,
        totalTickets: 300,
        description: 'Match de coupe - Quart de finale'
      },
      {
        id: '3',
        homeTeam: 'US Monastir',
        awayTeam: 'Espoir Sportif de Chorbane',
        matchDate: '2025-06-12T18:00:00',
        location: 'Stade Mustapha Ben Jannet, Monastir',
        ticketPrice: 25,
        availableTickets: 50,
        totalTickets: 100,
        description: 'Déplacement - Match de championnat'
      }
    ];
    setMatches(mockMatches);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTotalPrice = () => {
    return ticketTypes.reduce((total, type) => {
      return total + (selectedTickets[type.id] || 0) * type.price;
    }, 0);
  };

  const getTotalTickets = () => {
    return Object.values(selectedTickets).reduce((total, count) => total + count, 0);
  };

  const handleTicketChange = (typeId: string, count: number) => {
    setSelectedTickets(prev => ({
      ...prev,
      [typeId]: Math.max(0, count)
    }));
  };

  const handleReservation = async () => {
    if (!isAuthenticated) {
      alert('Vous devez être connecté pour réserver des billets');
      return;
    }

    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Success - move to confirmation step
      setCurrentStep(4);
    } catch (error) {
      alert('Erreur lors de la réservation. Veuillez réessayer.');
    } finally {
      setLoading(false);
    }
  };

  const renderMatchSelection = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choisissez votre match</h2>
        <p className="text-gray-600">Sélectionnez le match pour lequel vous souhaitez réserver des billets</p>
      </div>

      <div className="grid gap-6">
        {matches.map((match) => (
          <div
            key={match.id}
            className={`border rounded-lg p-6 cursor-pointer transition-all ${
              selectedMatch?.id === match.id
                ? 'border-green-500 bg-green-50'
                : 'border-gray-200 hover:border-green-300 hover:shadow-md'
            }`}
            onClick={() => setSelectedMatch(match)}
          >
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {match.homeTeam} vs {match.awayTeam}
                </h3>
                <div className="space-y-2 text-sm text-gray-600">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    {formatDate(match.matchDate)} à {formatTime(match.matchDate)}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2" />
                    {match.location}
                  </div>
                  <div className="flex items-center">
                    <Ticket className="h-4 w-4 mr-2" />
                    {match.availableTickets} billets disponibles sur {match.totalTickets}
                  </div>
                </div>
                {match.description && (
                  <p className="mt-2 text-sm text-gray-500">{match.description}</p>
                )}
              </div>
              <div className="text-right">
                <p className="text-lg font-bold text-green-600">À partir de {match.ticketPrice} DT</p>
                <div className="mt-2">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{
                        width: `${(match.availableTickets / match.totalTickets) * 100}%`
                      }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    {Math.round((match.availableTickets / match.totalTickets) * 100)}% disponible
                  </p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end">
        <button
          onClick={() => setCurrentStep(2)}
          disabled={!selectedMatch}
          className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continuer
        </button>
      </div>
    </div>
  );

  const renderTicketSelection = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Choisissez vos billets</h2>
        <p className="text-gray-600">
          {selectedMatch?.homeTeam} vs {selectedMatch?.awayTeam}
        </p>
        <p className="text-sm text-gray-500">
          {formatDate(selectedMatch?.matchDate || '')} à {formatTime(selectedMatch?.matchDate || '')}
        </p>
      </div>

      <div className="grid gap-4">
        {ticketTypes.map((type) => (
          <div key={type.id} className="border rounded-lg p-6">
            <div className="flex justify-between items-center">
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">{type.name}</h3>
                <p className="text-gray-600 text-sm">{type.description}</p>
                <p className="text-green-600 font-bold text-lg mt-1">{type.price} DT</p>
                <p className="text-xs text-gray-500">{type.available} billets disponibles</p>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => handleTicketChange(type.id, (selectedTickets[type.id] || 0) - 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  disabled={(selectedTickets[type.id] || 0) <= 0}
                >
                  -
                </button>
                <span className="w-8 text-center font-medium">
                  {selectedTickets[type.id] || 0}
                </span>
                <button
                  onClick={() => handleTicketChange(type.id, (selectedTickets[type.id] || 0) + 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                  disabled={(selectedTickets[type.id] || 0) >= type.available}
                >
                  +
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {getTotalTickets() > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium text-gray-900">
                Total: {getTotalTickets()} billet{getTotalTickets() > 1 ? 's' : ''}
              </p>
              <p className="text-sm text-gray-600">
                {Object.entries(selectedTickets)
                  .filter(([_, count]) => count > 0)
                  .map(([typeId, count]) => {
                    const type = ticketTypes.find(t => t.id === typeId);
                    return `${count} ${type?.name}`;
                  })
                  .join(', ')}
              </p>
            </div>
            <p className="text-2xl font-bold text-green-600">{getTotalPrice()} DT</p>
          </div>
        </div>
      )}

      <div className="flex justify-between">
        <button
          onClick={() => setCurrentStep(1)}
          className="btn btn-outline"
        >
          Retour
        </button>
        <button
          onClick={() => setCurrentStep(3)}
          disabled={getTotalTickets() === 0}
          className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Continuer
        </button>
      </div>
    </div>
  );

  const renderCustomerInfo = () => (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Informations de contact</h2>
        <p className="text-gray-600">Vérifiez et complétez vos informations</p>
      </div>

      <div className="bg-white rounded-lg border p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Prénom *
            </label>
            <input
              type="text"
              value={customerInfo.firstName}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, firstName: e.target.value }))}
              className="input"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nom *
            </label>
            <input
              type="text"
              value={customerInfo.lastName}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, lastName: e.target.value }))}
              className="input"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email *
            </label>
            <input
              type="email"
              value={customerInfo.email}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
              className="input"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Téléphone *
            </label>
            <input
              type="tel"
              value={customerInfo.phone}
              onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
              className="input"
              required
            />
          </div>
        </div>
      </div>

      {/* Order summary */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Récapitulatif de commande</h3>
        <div className="space-y-2">
          <div className="flex justify-between">
            <span>Match:</span>
            <span className="font-medium">
              {selectedMatch?.homeTeam} vs {selectedMatch?.awayTeam}
            </span>
          </div>
          <div className="flex justify-between">
            <span>Date:</span>
            <span>{formatDate(selectedMatch?.matchDate || '')}</span>
          </div>
          <div className="flex justify-between">
            <span>Lieu:</span>
            <span>{selectedMatch?.location}</span>
          </div>
          <hr className="my-3" />
          {Object.entries(selectedTickets)
            .filter(([_, count]) => count > 0)
            .map(([typeId, count]) => {
              const type = ticketTypes.find(t => t.id === typeId);
              return (
                <div key={typeId} className="flex justify-between">
                  <span>{count} × {type?.name}</span>
                  <span>{count * (type?.price || 0)} DT</span>
                </div>
              );
            })}
          <hr className="my-3" />
          <div className="flex justify-between text-lg font-bold">
            <span>Total:</span>
            <span className="text-green-600">{getTotalPrice()} DT</span>
          </div>
        </div>
      </div>

      <div className="flex justify-between">
        <button
          onClick={() => setCurrentStep(2)}
          className="btn btn-outline"
        >
          Retour
        </button>
        <button
          onClick={handleReservation}
          disabled={loading || !customerInfo.firstName || !customerInfo.lastName || !customerInfo.email || !customerInfo.phone}
          className="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Traitement...
            </div>
          ) : (
            <>
              <CreditCard className="h-5 w-5 mr-2" />
              Confirmer la réservation
            </>
          )}
        </button>
      </div>
    </div>
  );

  const renderConfirmation = () => (
    <div className="text-center space-y-6">
      <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
        <CheckCircle className="h-10 w-10 text-green-600" />
      </div>
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Réservation confirmée !</h2>
        <p className="text-gray-600">
          Votre réservation a été enregistrée avec succès. Vous recevrez un email de confirmation sous peu.
        </p>
      </div>

      <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-left max-w-md mx-auto">
        <h3 className="font-medium text-gray-900 mb-3">Détails de votre réservation</h3>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span>Numéro de réservation:</span>
            <span className="font-mono">#ESC-{Date.now().toString().slice(-6)}</span>
          </div>
          <div className="flex justify-between">
            <span>Match:</span>
            <span>{selectedMatch?.homeTeam} vs {selectedMatch?.awayTeam}</span>
          </div>
          <div className="flex justify-between">
            <span>Billets:</span>
            <span>{getTotalTickets()}</span>
          </div>
          <div className="flex justify-between font-medium">
            <span>Total payé:</span>
            <span>{getTotalPrice()} DT</span>
          </div>
        </div>
      </div>

      <div className="space-y-3">
        <button
          onClick={() => window.print()}
          className="btn btn-outline"
        >
          Imprimer la confirmation
        </button>
        <div>
          <a href="/dashboard" className="btn btn-primary">
            Retour au tableau de bord
          </a>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-xl">ESC</span>
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Réservation de billets</h1>
          </div>
          <p className="text-gray-600">Espoir Sportif de Chorbane</p>
        </div>

        {/* Progress indicator */}
        <div className="mb-8">
          <div className="flex items-center justify-center space-x-4">
            {[1, 2, 3, 4].map((step) => (
              <div key={step} className="flex items-center">
                <div className={`
                  w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                  ${currentStep >= step
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                  }
                `}>
                  {step}
                </div>
                {step < 4 && (
                  <div className={`
                    w-16 h-1 mx-2
                    ${currentStep > step ? 'bg-green-600' : 'bg-gray-200'}
                  `} />
                )}
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-2">
            <div className="grid grid-cols-4 gap-8 text-xs text-gray-600">
              <span>Match</span>
              <span>Billets</span>
              <span>Informations</span>
              <span>Confirmation</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="bg-white rounded-lg shadow-lg p-8">
          {currentStep === 1 && renderMatchSelection()}
          {currentStep === 2 && renderTicketSelection()}
          {currentStep === 3 && renderCustomerInfo()}
          {currentStep === 4 && renderConfirmation()}
        </div>

        {/* Info */}
        {currentStep < 4 && (
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex">
              <Info className="h-5 w-5 text-blue-600 mr-2 mt-0.5" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Informations importantes :</p>
                <ul className="list-disc list-inside space-y-1">
                  <li>Les billets sont nominatifs et non remboursables</li>
                  <li>Présentation d'une pièce d'identité obligatoire à l'entrée</li>
                  <li>Ouverture des portes 1h avant le coup d'envoi</li>
                  <li>Paiement sécurisé par carte bancaire ou espèces</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReservationPage;
