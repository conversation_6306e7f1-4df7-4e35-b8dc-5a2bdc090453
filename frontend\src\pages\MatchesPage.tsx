import React from 'react';
import { Calendar, MapPin, Clock } from 'lucide-react';

const MatchesPage = () => {
  const matches = [
    {
      id: 1,
      date: '2025-05-28',
      time: '15:00',
      homeTeam: 'Espoir Sportif de Chorbane',
      awayTeam: 'AS Soliman',
      venue: 'Stade Municipal Chorbane',
      status: 'upcoming',
      homeScore: null,
      awayScore: null,
    },
    {
      id: 2,
      date: '2025-05-21',
      time: '16:00',
      homeTeam: 'CA Bizertin',
      awayTeam: 'Espoir Sportive Chorbane',
      venue: 'Stade de Bizerte',
      status: 'finished',
      homeScore: 1,
      awayScore: 3,
    },
    {
      id: 3,
      date: '2025-05-14',
      time: '15:30',
      homeTeam: 'Espoir Sportive Chorbane',
      awayTeam: 'US Monastir',
      venue: 'Stade Municipal Chorbane',
      status: 'finished',
      homeScore: 2,
      awayScore: 1,
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="gradient-hero text-white section-padding">
        <div className="container-custom text-center">
          <h1 className="hero-title mb-6">Calendrier des Matchs</h1>
          <p className="hero-subtitle mx-auto">
            Suivez tous nos matchs et ne manquez aucun moment fort de la saison
          </p>
        </div>
      </section>

      {/* Matches List */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto space-y-6">
            {matches.map((match) => (
              <div key={match.id} className="card card-hover p-6">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
                  {/* Match Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(match.date)}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <Clock className="w-4 h-4" />
                        <span>{match.time}</span>
                      </div>
                    </div>

                    {/* Teams */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center mb-2">
                            <span className="text-white font-bold text-sm">
                              {match.homeTeam.includes('Espoir') ? 'ES' : match.homeTeam.substring(0, 2)}
                            </span>
                          </div>
                          <p className="text-sm font-medium text-dark-900 max-w-20 truncate">
                            {match.homeTeam}
                          </p>
                        </div>

                        <div className="text-center mx-8">
                          {match.status === 'finished' ? (
                            <div className="text-2xl font-bold text-dark-900">
                              {match.homeScore} - {match.awayScore}
                            </div>
                          ) : (
                            <div className="text-xl font-bold text-gray-400">VS</div>
                          )}
                        </div>

                        <div className="text-center">
                          <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center mb-2">
                            <span className="text-gray-600 font-bold text-sm">
                              {match.awayTeam.includes('Espoir') ? 'ES' : match.awayTeam.substring(0, 2)}
                            </span>
                          </div>
                          <p className="text-sm font-medium text-dark-900 max-w-20 truncate">
                            {match.awayTeam}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MapPin className="w-4 h-4" />
                      <span>{match.venue}</span>
                    </div>
                  </div>

                  {/* Status & Action */}
                  <div className="mt-4 lg:mt-0 lg:ml-6 text-center lg:text-right">
                    {match.status === 'upcoming' ? (
                      <>
                        <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mb-3">
                          À venir
                        </div>
                        <div>
                          <button className="btn btn-primary btn-sm">
                            Réserver
                          </button>
                        </div>
                      </>
                    ) : (
                      <>
                        <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mb-3">
                          Terminé
                        </div>
                        <div>
                          <button className="btn btn-outline btn-sm">
                            Résumé
                          </button>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default MatchesPage;
