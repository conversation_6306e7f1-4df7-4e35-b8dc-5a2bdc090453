# Documentation Technique - Espoir Sportive Chorbane

## 📋 Vue d'ensemble

Cette documentation technique détaille l'architecture, les choix technologiques et les processus de développement de l'application de gestion d'équipe de football Espoir Sportive Chorbane.

## 🏗️ Architecture du Système

### Architecture Générale
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   React + Vite  │◄──►│  Node.js/Express│◄──►│  PostgreSQL     │
│   TypeScript    │    │     Prisma      │    │                 │
│   Tailwind CSS  │    │      JWT        │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Flux de Données
1. **Frontend** → API REST → **Backend**
2. **Backend** → Prisma ORM → **PostgreSQL**
3. **Authentification** : JWT tokens
4. **Validation** : express-validator + Prisma schema

## 🗄️ Base de Données

### Schéma de Base de Données

#### Entités Principales
- **User** : Utilisateurs du système (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>er)
- **Player** : Profils des joueurs avec statistiques
- **Match** : Matchs de l'équipe avec résultats
- **Training** : Entraînements et présences
- **Article** : Système de blog/actualités
- **Gallery** : Galeries multimédia

#### Relations Clés
```sql
User 1:1 Player (optionnel)
Player 1:N PlayerStats (par saison)
Match N:N Player (via MatchPlayer)
Training N:N Player (via TrainingAttendance)
User 1:N Article (auteur)
Article N:N Tag (via ArticleTag)
Gallery 1:N GalleryMedia
```

### Énumérations
- **UserRole** : ADMIN, PLAYER, SUPPORTER
- **PlayerPosition** : GOALKEEPER, DEFENDER, MIDFIELDER, FORWARD
- **MatchStatus** : SCHEDULED, LIVE, FINISHED, CANCELLED, POSTPONED
- **CardType** : YELLOW, RED

## 🔧 Backend - API Node.js/Express

### Structure des Dossiers
```
backend/
├── src/
│   ├── config/
│   │   └── database.js          # Configuration Prisma
│   ├── middleware/
│   │   └── auth.js              # Middlewares JWT et rôles
│   ├── routes/
│   │   ├── auth.js              # Authentification
│   │   ├── users.js             # Gestion utilisateurs
│   │   ├── players.js           # Gestion joueurs
│   │   ├── matches.js           # Gestion matchs
│   │   ├── trainings.js         # Gestion entraînements
│   │   ├── articles.js          # Gestion articles
│   │   ├── galleries.js         # Gestion galeries
│   │   └── stats.js             # Statistiques
│   ├── utils/
│   │   ├── auth.js              # Utilitaires authentification
│   │   └── seed.js              # Script de données test
│   └── server.js                # Point d'entrée
├── prisma/
│   └── schema.prisma            # Schéma base de données
├── package.json
└── .env                         # Variables d'environnement
```

### Middlewares Implémentés

#### Authentification
- `authenticateToken` : Vérification JWT
- `requireRole` : Contrôle d'accès par rôle
- `requireAdmin` : Accès admin uniquement
- `requirePlayerOrAdmin` : Accès joueur ou admin
- `requireOwnershipOrAdmin` : Accès propriétaire ou admin

#### Sécurité
- **Helmet.js** : Headers de sécurité
- **CORS** : Configuration cross-origin
- **Rate Limiting** : 100 req/15min par IP
- **express-validator** : Validation des entrées

### API Endpoints

#### Authentification (`/api/auth`)
- `POST /register` : Inscription utilisateur
- `POST /login` : Connexion utilisateur
- `GET /me` : Profil utilisateur actuel
- `GET /verify` : Vérification token

#### Utilisateurs (`/api/users`)
- `GET /` : Liste utilisateurs (Admin)
- `GET /:id` : Détails utilisateur
- `PUT /:id` : Modification utilisateur
- `DELETE /:id` : Suppression utilisateur (Admin)
- `PUT /:id/password` : Changement mot de passe

#### Joueurs (`/api/players`)
- `GET /` : Liste joueurs avec filtres
- `GET /:id` : Détails joueur avec stats
- `POST /` : Création joueur (Admin)
- `PUT /:id` : Modification joueur (Admin)

#### Matchs (`/api/matches`)
- `GET /` : Liste matchs avec filtres
- `GET /:id` : Détails match avec composition
- `POST /` : Création match (Admin)
- `PUT /:id` : Modification match (Admin)
- `POST /:id/players` : Sélection joueurs (Admin)
- `DELETE /:id` : Suppression match (Admin)

#### Entraînements (`/api/trainings`)
- `GET /` : Liste entraînements
- `GET /:id` : Détails entraînement avec présences
- `POST /` : Création entraînement (Admin)
- `PUT /:id` : Modification entraînement (Admin)
- `PUT /:id/attendance` : Gestion présences (Admin)
- `DELETE /:id` : Suppression entraînement (Admin)

#### Articles (`/api/articles`)
- `GET /` : Liste articles (publics ou tous si admin)
- `GET /:id` : Détails article avec commentaires
- `POST /` : Création article (Admin)
- `PUT /:id` : Modification article (Admin)
- `DELETE /:id` : Suppression article (Admin)

#### Galeries (`/api/galleries`)
- `GET /` : Liste galeries
- `GET /:id` : Détails galerie avec médias
- `POST /` : Création galerie (Admin)
- `PUT /:id` : Modification galerie (Admin)
- `POST /:id/media` : Ajout médias (Admin)
- `DELETE /:id/media/:mediaId` : Suppression média (Admin)
- `DELETE /:id` : Suppression galerie (Admin)

#### Statistiques (`/api/stats`)
- `GET /team` : Statistiques équipe
- `GET /player/:id` : Statistiques joueur
- `GET /dashboard` : Tableau de bord

### Validation des Données

#### Règles de Validation
- **Email** : Format valide + normalisation
- **Mot de passe** : 8+ caractères, majuscule, minuscule, chiffre, caractère spécial
- **Numéro maillot** : 1-99, unique
- **Position joueur** : Énumération stricte
- **Dates** : Format ISO8601
- **Scores** : Entiers positifs

#### Gestion d'Erreurs
- Validation centralisée avec express-validator
- Messages d'erreur en français
- Codes HTTP appropriés
- Logs détaillés pour le debugging

## 🎨 Frontend - React/TypeScript

### Structure Prévue
```
frontend/
├── src/
│   ├── components/
│   │   ├── common/              # Composants réutilisables
│   │   ├── layout/              # Layout et navigation
│   │   └── forms/               # Formulaires
│   ├── pages/
│   │   ├── auth/                # Pages authentification
│   │   ├── dashboard/           # Tableau de bord
│   │   ├── players/             # Gestion joueurs
│   │   ├── matches/             # Gestion matchs
│   │   ├── trainings/           # Gestion entraînements
│   │   ├── articles/            # Blog/actualités
│   │   └── galleries/           # Galeries
│   ├── hooks/
│   │   ├── useAuth.ts           # Hook authentification
│   │   ├── useApi.ts            # Hook appels API
│   │   └── useLocalStorage.ts   # Hook stockage local
│   ├── services/
│   │   ├── api.ts               # Configuration Axios
│   │   ├── auth.service.ts      # Service authentification
│   │   └── *.service.ts         # Services par entité
│   ├── utils/
│   │   ├── constants.ts         # Constantes
│   │   ├── helpers.ts           # Fonctions utilitaires
│   │   └── validators.ts        # Validations frontend
│   ├── types/
│   │   └── index.ts             # Types TypeScript
│   └── styles/
│       └── globals.css          # Styles Tailwind
├── public/
├── package.json
└── vite.config.ts
```

### Technologies Frontend
- **React 18** : Framework principal
- **TypeScript** : Typage statique
- **Vite** : Build tool rapide
- **Tailwind CSS** : Framework CSS utilitaire
- **React Router** : Navigation SPA
- **Axios** : Client HTTP
- **React Hook Form** : Gestion formulaires
- **Chart.js** : Graphiques statistiques
- **React Query** : Cache et synchronisation données

## 🔐 Sécurité

### Authentification JWT
- **Algorithme** : HS256
- **Expiration** : 7 jours (configurable)
- **Refresh** : Pas implémenté (à ajouter)
- **Stockage** : localStorage (frontend)

### Hashage Mots de Passe
- **Algorithme** : bcrypt
- **Salt rounds** : 12
- **Validation** : Complexité forcée

### Protection CSRF/XSS
- **Helmet.js** : Headers sécurisés
- **CORS** : Origine contrôlée
- **Validation** : Sanitisation entrées
- **Rate Limiting** : Protection brute force

### Contrôle d'Accès
- **Rôles** : ADMIN, PLAYER, SUPPORTER
- **Permissions** : Par route et méthode
- **Ownership** : Vérification propriétaire

## 📊 Données de Test

### Script de Seed
Le script `backend/src/utils/seed.js` génère :
- 1 administrateur
- 11 joueurs avec positions réalistes
- 10 matchs (passés et futurs)
- 8 entraînements avec présences
- 3 articles de blog
- 1 galerie avec médias
- Tags et statistiques

### Comptes de Test
- **Admin** : `<EMAIL>` / `admin123`
- **Joueur** : `<EMAIL>` / `player123`
- **Supporter** : `<EMAIL>` / `supporter123`

## 🧪 Tests et Qualité

### Tests Backend (À implémenter)
- **Unit tests** : Jest + Supertest
- **Integration tests** : API endpoints
- **Database tests** : Prisma mocks

### Tests Frontend (À implémenter)
- **Unit tests** : Jest + React Testing Library
- **Component tests** : Storybook
- **E2E tests** : Cypress

### Qualité Code
- **ESLint** : Linting JavaScript/TypeScript
- **Prettier** : Formatage code
- **Husky** : Git hooks (à configurer)
- **TypeScript** : Typage statique

## 🚀 Déploiement

### Environnements
- **Development** : Local avec hot reload
- **Staging** : À configurer
- **Production** : À configurer

### Docker (À implémenter)
```dockerfile
# Backend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 5000
CMD ["npm", "start"]
```

### Variables d'Environnement
```bash
# Backend
DATABASE_URL=postgresql://...
JWT_SECRET=...
PORT=5000
NODE_ENV=production

# Frontend
VITE_API_URL=https://api.espoir-sportive.com
VITE_APP_NAME=Espoir Sportive Chorbane
```

## 📈 Performance

### Backend
- **Prisma** : ORM optimisé avec requêtes SQL efficaces
- **Pagination** : Toutes les listes paginées
- **Indexation** : Index sur champs recherchés
- **Cache** : À implémenter (Redis)

### Frontend
- **Vite** : Build rapide et HMR
- **Code splitting** : Lazy loading des routes
- **Optimisation images** : À implémenter
- **PWA** : À implémenter

## 🔧 Maintenance

### Logs
- **Morgan** : Logs HTTP
- **Console** : Logs applicatifs
- **Rotation** : À configurer

### Monitoring
- **Health check** : `/api/health`
- **Métriques** : À implémenter
- **Alertes** : À configurer

### Backup
- **Base de données** : Backup automatique PostgreSQL
- **Fichiers** : Backup uploads
- **Configuration** : Versioning Git

---

*Documentation technique mise à jour le 24/05/2025*
