@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-white text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Boutons */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-green-500 text-white hover:bg-green-600 focus:ring-green-400 shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-yellow-500 text-gray-900 hover:bg-yellow-600 focus:ring-yellow-400 shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply border-2 border-green-500 text-green-500 hover:bg-green-500 hover:text-white focus:ring-green-400;
  }

  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 focus:ring-gray-300;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-md {
    @apply px-4 py-2 text-base;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* Cards */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-medium hover:-translate-y-1;
  }

  /* Navigation */
  .nav-link {
    @apply text-gray-600 hover:text-green-600 font-medium transition-colors duration-200 relative;
  }

  .nav-link.active {
    @apply text-green-600;
  }

  .nav-link.active::after {
    @apply content-[''] absolute bottom-0 left-0 w-full h-0.5 bg-green-600;
  }

  /* Inputs */
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200;
  }

  /* Gradients */
  .gradient-primary {
    @apply bg-gradient-to-r from-green-500 to-green-600;
  }

  .gradient-secondary {
    @apply bg-gradient-to-r from-yellow-500 to-yellow-600;
  }

  .gradient-hero {
    @apply bg-gradient-to-br from-green-500 via-green-600 to-gray-800;
  }

  /* Animations */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Sections */
  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Hero styles */
  .hero-title {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold font-display leading-tight;
  }

  .hero-subtitle {
    @apply text-lg md:text-xl text-gray-600 max-w-2xl;
  }

  /* Stats */
  .stat-number {
    @apply text-3xl md:text-4xl font-bold text-green-600;
  }

  .stat-label {
    @apply text-sm md:text-base text-gray-600 font-medium;
  }
}
