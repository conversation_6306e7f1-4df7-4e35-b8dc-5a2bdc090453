import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  MapPin,
  Clock,
  Users,
  Trophy,
  Filter
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';
import MatchModal from '../../components/dashboard/MatchModal';

interface Match {
  id: string;
  homeTeam: string;
  awayTeam: string;
  matchDate: string;
  location: string;
  status: 'SCHEDULED' | 'LIVE' | 'FINISHED' | 'CANCELLED';
  homeScore?: number;
  awayScore?: number;
  competition: string;
  ticketPrice?: number;
  availableTickets?: number;
}

const MatchesManagement: React.FC = () => {
  const [matches, setMatches] = useState<Match[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingMatch, setEditingMatch] = useState<Match | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - Replace with API call
  useEffect(() => {
    const mockMatches: Match[] = [
      {
        id: '1',
        homeTeam: 'Espoir Sportif de Chorbane',
        awayTeam: 'AS Soliman',
        matchDate: '2025-05-28T15:00:00',
        location: 'Stade Municipal Chorbane',
        status: 'SCHEDULED',
        competition: 'Championnat National',
        ticketPrice: 15,
        availableTickets: 250
      },
      {
        id: '2',
        homeTeam: 'CA Bizertin',
        awayTeam: 'Espoir Sportif de Chorbane',
        matchDate: '2025-05-20T16:30:00',
        location: 'Stade 15 Octobre, Bizerte',
        status: 'FINISHED',
        homeScore: 1,
        awayScore: 3,
        competition: 'Championnat National'
      },
      {
        id: '3',
        homeTeam: 'Espoir Sportif de Chorbane',
        awayTeam: 'Club Africain',
        matchDate: '2025-06-05T18:00:00',
        location: 'Stade Municipal Chorbane',
        status: 'SCHEDULED',
        competition: 'Coupe de Tunisie',
        ticketPrice: 25,
        availableTickets: 180
      }
    ];
    setMatches(mockMatches);
    setLoading(false);
  }, []);

  const filteredMatches = matches.filter(match => {
    const matchesSearch =
      match.homeTeam.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.awayTeam.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.competition.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'ALL' || match.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('fr-FR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      SCHEDULED: { color: 'bg-blue-100 text-blue-800', text: 'Programmé' },
      LIVE: { color: 'bg-green-100 text-green-800', text: 'En cours' },
      FINISHED: { color: 'bg-gray-100 text-gray-800', text: 'Terminé' },
      CANCELLED: { color: 'bg-red-100 text-red-800', text: 'Annulé' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const handleDeleteMatch = (matchId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce match ?')) {
      setMatches(matches.filter(match => match.id !== matchId));
    }
  };

  const handleSaveMatch = (matchData: Match) => {
    if (editingMatch) {
      // Update existing match
      setMatches(matches.map(match =>
        match.id === editingMatch.id ? { ...matchData, id: editingMatch.id } : match
      ));
      setEditingMatch(null);
    } else {
      // Add new match
      const newMatch = { ...matchData, id: Date.now().toString() };
      setMatches([...matches, newMatch]);
    }
    setShowAddModal(false);
  };

  const handleEditMatch = (match: Match) => {
    setEditingMatch(match);
    setShowAddModal(true);
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setEditingMatch(null);
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des matchs</h1>
            <p className="mt-1 text-sm text-gray-600">
              Gérez le calendrier des matchs d'Espoir Sportif de Chorbane
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              onClick={() => window.location.href = '/dashboard/matches/add'}
              className="btn btn-primary"
            >
              <Plus className="h-5 w-5 mr-2" />
              Nouveau match
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Rechercher un match..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="input pl-10"
              >
                <option value="ALL">Tous les statuts</option>
                <option value="SCHEDULED">Programmés</option>
                <option value="LIVE">En cours</option>
                <option value="FINISHED">Terminés</option>
                <option value="CANCELLED">Annulés</option>
              </select>
            </div>
            <div className="text-sm text-gray-600 flex items-center">
              <Trophy className="h-4 w-4 mr-2" />
              {filteredMatches.length} match{filteredMatches.length > 1 ? 's' : ''} trouvé{filteredMatches.length > 1 ? 's' : ''}
            </div>
          </div>
        </div>

        {/* Matches List */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Match
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date & Heure
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Lieu
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Billets
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredMatches.map((match) => (
                  <tr key={match.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {match.homeTeam} vs {match.awayTeam}
                        </div>
                        <div className="text-sm text-gray-500">{match.competition}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(match.matchDate)}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatTime(match.matchDate)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 flex items-center">
                        <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                        {match.location}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(match.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {match.status === 'FINISHED' ? (
                        <div className="text-sm font-medium text-gray-900">
                          {match.homeScore} - {match.awayScore}
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {match.availableTickets !== undefined ? (
                        <div className="text-sm text-gray-900">
                          <div>{match.availableTickets} disponibles</div>
                          <div className="text-xs text-gray-500">{match.ticketPrice} DT</div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">-</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <button
                          onClick={() => handleEditMatch(match)}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteMatch(match.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredMatches.length === 0 && (
          <div className="text-center py-12">
            <Trophy className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun match trouvé</h3>
            <p className="mt-1 text-sm text-gray-500">
              Commencez par ajouter un nouveau match.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowAddModal(true)}
                className="btn btn-primary"
              >
                <Plus className="h-5 w-5 mr-2" />
                Nouveau match
              </button>
            </div>
          </div>
        )}

        {/* Match Modal */}
        <MatchModal
          isOpen={showAddModal}
          onClose={handleCloseModal}
          onSave={handleSaveMatch}
          match={editingMatch}
        />
      </div>
    </DashboardLayout>
  );
};

export default MatchesManagement;
