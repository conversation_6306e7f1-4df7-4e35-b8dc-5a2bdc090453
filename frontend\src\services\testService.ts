// Test simple pour vérifier que les services fonctionnent
import { playerService } from './playerService';
import { authService } from './authService';

// Test de base pour vérifier les imports
console.log('Services importés avec succès:');
console.log('- playerService:', typeof playerService);
console.log('- authService:', typeof authService);

// Test des méthodes de base
export const testServices = () => {
  console.log('Test des services...');
  
  // Test authService
  console.log('authService.isAuthenticated():', authService.isAuthenticated());
  console.log('authService.getToken():', authService.getToken());
  
  // Test playerService (sans appel réseau)
  const testPlayerData = {
    firstName: 'Test',
    lastName: 'Player',
    email: '<EMAIL>',
    dateOfBirth: '1995-01-01',
    nationality: 'Tunisie',
    position: 'MIDFIELDER' as const,
    jerseyNumber: 99,
    height: 180,
    weight: 75,
    preferredFoot: 'RIGHT' as const
  };
  
  const validationErrors = playerService.validatePlayerData(testPlayerData);
  console.log('Validation des données joueur:', validationErrors);
  
  console.log('Tests terminés avec succès !');
};

export default testServices;
