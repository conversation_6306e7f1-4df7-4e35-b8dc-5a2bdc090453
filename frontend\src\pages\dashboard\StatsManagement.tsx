import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Trophy,
  Target,
  Calendar,
  DollarSign,
  Activity,
  Award
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface PlayerStats {
  id: string;
  name: string;
  position: string;
  goals: number;
  assists: number;
  matches: number;
  yellowCards: number;
  redCards: number;
}

interface TeamStats {
  totalMatches: number;
  wins: number;
  draws: number;
  losses: number;
  goalsFor: number;
  goalsAgainst: number;
  cleanSheets: number;
  winPercentage: number;
}

interface FinancialStats {
  ticketRevenue: number;
  sponsorshipRevenue: number;
  merchandiseRevenue: number;
  totalRevenue: number;
  expenses: number;
  netProfit: number;
}

const StatsManagement: React.FC = () => {
  const [playerStats, setPlayerStats] = useState<PlayerStats[]>([]);
  const [teamStats, setTeamStats] = useState<TeamStats | null>(null);
  const [financialStats, setFinancialStats] = useState<FinancialStats | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('2024-2025');
  const [loading, setLoading] = useState(true);

  // Mock data - Replace with API call
  useEffect(() => {
    const mockPlayerStats: PlayerStats[] = [
      {
        id: '1',
        name: 'Hamza Bedoui',
        position: 'Attaquant',
        goals: 15,
        assists: 8,
        matches: 22,
        yellowCards: 3,
        redCards: 0
      },
      {
        id: '2',
        name: 'Ahmed Khalil',
        position: 'Milieu',
        goals: 7,
        assists: 12,
        matches: 24,
        yellowCards: 5,
        redCards: 1
      },
      {
        id: '3',
        name: 'Mohamed Sassi',
        position: 'Défenseur',
        goals: 2,
        assists: 3,
        matches: 25,
        yellowCards: 8,
        redCards: 0
      },
      {
        id: '4',
        name: 'Youssef Ben Ali',
        position: 'Gardien',
        goals: 0,
        assists: 0,
        matches: 20,
        yellowCards: 1,
        redCards: 0
      }
    ];

    const mockTeamStats: TeamStats = {
      totalMatches: 25,
      wins: 15,
      draws: 6,
      losses: 4,
      goalsFor: 42,
      goalsAgainst: 18,
      cleanSheets: 12,
      winPercentage: 60
    };

    const mockFinancialStats: FinancialStats = {
      ticketRevenue: 45000,
      sponsorshipRevenue: 120000,
      merchandiseRevenue: 15000,
      totalRevenue: 180000,
      expenses: 150000,
      netProfit: 30000
    };

    setPlayerStats(mockPlayerStats);
    setTeamStats(mockTeamStats);
    setFinancialStats(mockFinancialStats);
    setLoading(false);
  }, [selectedPeriod]);

  const getPositionColor = (position: string) => {
    const colors = {
      'Gardien': 'bg-yellow-100 text-yellow-800',
      'Défenseur': 'bg-blue-100 text-blue-800',
      'Milieu': 'bg-green-100 text-green-800',
      'Attaquant': 'bg-red-100 text-red-800'
    };
    return colors[position as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Statistiques</h1>
            <p className="mt-1 text-sm text-gray-600">
              Analyse des performances d'Espoir Sportif de Chorbane
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="input"
            >
              <option value="2024-2025">Saison 2024-2025</option>
              <option value="2023-2024">Saison 2023-2024</option>
              <option value="2022-2023">Saison 2022-2023</option>
            </select>
          </div>
        </div>

        {/* Team Performance Stats */}
        {teamStats && (
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Performance de l'équipe</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Calendar className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Matchs joués</p>
                    <p className="text-2xl font-bold text-gray-900">{teamStats.totalMatches}</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Trophy className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Victoires</p>
                    <p className="text-2xl font-bold text-gray-900">{teamStats.wins}</p>
                    <p className="text-xs text-green-600 flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      {teamStats.winPercentage}%
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Target className="h-6 w-6 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Buts marqués</p>
                    <p className="text-2xl font-bold text-gray-900">{teamStats.goalsFor}</p>
                    <p className="text-xs text-gray-500">
                      {(teamStats.goalsFor / teamStats.totalMatches).toFixed(1)} par match
                    </p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Award className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Clean Sheets</p>
                    <p className="text-2xl font-bold text-gray-900">{teamStats.cleanSheets}</p>
                    <p className="text-xs text-gray-500">
                      {((teamStats.cleanSheets / teamStats.totalMatches) * 100).toFixed(0)}% des matchs
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Team Performance Chart */}
            <div className="bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Répartition des résultats</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-green-600">{teamStats.wins}</div>
                  <div className="text-sm text-gray-600">Victoires</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${(teamStats.wins / teamStats.totalMatches) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-yellow-600">{teamStats.draws}</div>
                  <div className="text-sm text-gray-600">Nuls</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-yellow-600 h-2 rounded-full" 
                      style={{ width: `${(teamStats.draws / teamStats.totalMatches) * 100}%` }}
                    ></div>
                  </div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-red-600">{teamStats.losses}</div>
                  <div className="text-sm text-gray-600">Défaites</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div 
                      className="bg-red-600 h-2 rounded-full" 
                      style={{ width: `${(teamStats.losses / teamStats.totalMatches) * 100}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Financial Stats */}
        {financialStats && (
          <div className="mb-8">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statistiques financières</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <DollarSign className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Revenus totaux</p>
                    <p className="text-2xl font-bold text-gray-900">{financialStats.totalRevenue.toLocaleString()} DT</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Activity className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Billets</p>
                    <p className="text-2xl font-bold text-gray-900">{financialStats.ticketRevenue.toLocaleString()} DT</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Sponsoring</p>
                    <p className="text-2xl font-bold text-gray-900">{financialStats.sponsorshipRevenue.toLocaleString()} DT</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${financialStats.netProfit >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                    {financialStats.netProfit >= 0 ? (
                      <TrendingUp className="h-6 w-6 text-green-600" />
                    ) : (
                      <TrendingDown className="h-6 w-6 text-red-600" />
                    )}
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Bénéfice net</p>
                    <p className={`text-2xl font-bold ${financialStats.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {financialStats.netProfit.toLocaleString()} DT
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Player Stats */}
        <div>
          <h2 className="text-lg font-medium text-gray-900 mb-4">Statistiques des joueurs</h2>
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Joueur
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Matchs
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Buts
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Passes D.
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cartons
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Performance
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {playerStats.map((player) => (
                    <tr key={player.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{player.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPositionColor(player.position)}`}>
                          {player.position}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">{player.matches}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{player.goals}</div>
                        <div className="text-xs text-gray-500">
                          {player.matches > 0 ? (player.goals / player.matches).toFixed(2) : '0.00'} par match
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{player.assists}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex space-x-2">
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                            {player.yellowCards} J
                          </span>
                          {player.redCards > 0 && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                              {player.redCards} R
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                            <div 
                              className="bg-green-600 h-2 rounded-full" 
                              style={{ 
                                width: `${Math.min(((player.goals + player.assists) / Math.max(player.matches, 1)) * 100, 100)}%` 
                              }}
                            ></div>
                          </div>
                          <span className="text-xs text-gray-600">
                            {((player.goals + player.assists) / Math.max(player.matches, 1)).toFixed(1)}
                          </span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default StatsManagement;
