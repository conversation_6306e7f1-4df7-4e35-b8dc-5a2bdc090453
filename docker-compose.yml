version: '3.8'

services:
  # Base de données PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: espoir-sportive-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: espoir_sportive_db
      POSTGRES_USER: espoir_user
      POSTGRES_PASSWORD: espoir_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - espoir-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U espoir_user -d espoir_sportive_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Node.js
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: espoir-sportive-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: ******************************************************/espoir_sportive_db?schema=public
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-this-in-production}
      JWT_EXPIRES_IN: 7d
      PORT: 5000
      FRONTEND_URL: http://localhost:3000
      UPLOAD_DIR: uploads
      MAX_FILE_SIZE: 5242880
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "5000:5000"
    networks:
      - espoir-network
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend React
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        VITE_API_URL: http://localhost:5000/api
        VITE_APP_NAME: "Espoir Sportive Chorbane"
    container_name: espoir-sportive-frontend
    restart: unless-stopped
    ports:
      - "3000:80"
    networks:
      - espoir-network
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (optionnel pour production)
  nginx:
    image: nginx:alpine
    container_name: espoir-sportive-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./backend/uploads:/var/www/uploads:ro
    networks:
      - espoir-network
    depends_on:
      - frontend
      - backend
    profiles:
      - production

  # Redis pour cache (optionnel)
  redis:
    image: redis:7-alpine
    container_name: espoir-sportive-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - espoir-network
    profiles:
      - cache

  # Adminer pour gestion base de données (développement)
  adminer:
    image: adminer:latest
    container_name: espoir-sportive-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    networks:
      - espoir-network
    depends_on:
      - postgres
    profiles:
      - development

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  espoir-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
