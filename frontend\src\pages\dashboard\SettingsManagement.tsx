import React, { useState } from 'react';
import {
  Settings,
  User,
  Shield,
  Bell,
  Palette,
  Globe,
  Database,
  Mail,
  Save,
  Upload,
  Eye,
  EyeOff
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface ClubSettings {
  name: string;
  shortName: string;
  foundedYear: number;
  stadium: string;
  capacity: number;
  address: string;
  phone: string;
  email: string;
  website: string;
  logo: string;
  colors: {
    primary: string;
    secondary: string;
  };
}

interface NotificationSettings {
  emailNotifications: boolean;
  matchReminders: boolean;
  newsUpdates: boolean;
  reservationAlerts: boolean;
  systemAlerts: boolean;
}

interface SecuritySettings {
  twoFactorAuth: boolean;
  sessionTimeout: number;
  passwordExpiry: number;
  loginAttempts: number;
}

const SettingsManagement: React.FC = () => {
  const [activeTab, setActiveTab] = useState('club');
  const [showPassword, setShowPassword] = useState(false);
  const [saving, setSaving] = useState(false);

  const [clubSettings, setClubSettings] = useState<ClubSettings>({
    name: 'Espoir Sportif de Chorbane',
    shortName: 'ESC',
    foundedYear: 1950,
    stadium: 'Stade Municipal de Chorbane',
    capacity: 5000,
    address: 'Avenue Habib Bourguiba, Chorbane, Mahdia, Tunisie',
    phone: '+216 70 123 456',
    email: '<EMAIL>',
    website: 'https://www.espoir-sportif.com',
    logo: '/images/logo.png',
    colors: {
      primary: '#16a34a',
      secondary: '#eab308'
    }
  });

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailNotifications: true,
    matchReminders: true,
    newsUpdates: false,
    reservationAlerts: true,
    systemAlerts: true
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAttempts: 5
  });

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Paramètres sauvegardés avec succès !');
    } catch (error) {
      alert('Erreur lors de la sauvegarde');
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'club', name: 'Club', icon: Settings },
    { id: 'profile', name: 'Profil', icon: User },
    { id: 'security', name: 'Sécurité', icon: Shield },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'appearance', name: 'Apparence', icon: Palette },
    { id: 'system', name: 'Système', icon: Database }
  ];

  const renderClubSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Informations du club</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nom complet
            </label>
            <input
              type="text"
              value={clubSettings.name}
              onChange={(e) => setClubSettings({...clubSettings, name: e.target.value})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nom abrégé
            </label>
            <input
              type="text"
              value={clubSettings.shortName}
              onChange={(e) => setClubSettings({...clubSettings, shortName: e.target.value})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Année de fondation
            </label>
            <input
              type="number"
              value={clubSettings.foundedYear}
              onChange={(e) => setClubSettings({...clubSettings, foundedYear: parseInt(e.target.value)})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Stade
            </label>
            <input
              type="text"
              value={clubSettings.stadium}
              onChange={(e) => setClubSettings({...clubSettings, stadium: e.target.value})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Capacité du stade
            </label>
            <input
              type="number"
              value={clubSettings.capacity}
              onChange={(e) => setClubSettings({...clubSettings, capacity: parseInt(e.target.value)})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Téléphone
            </label>
            <input
              type="tel"
              value={clubSettings.phone}
              onChange={(e) => setClubSettings({...clubSettings, phone: e.target.value})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email
            </label>
            <input
              type="email"
              value={clubSettings.email}
              onChange={(e) => setClubSettings({...clubSettings, email: e.target.value})}
              className="input"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Site web
            </label>
            <input
              type="url"
              value={clubSettings.website}
              onChange={(e) => setClubSettings({...clubSettings, website: e.target.value})}
              className="input"
            />
          </div>
        </div>
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Adresse complète
          </label>
          <textarea
            value={clubSettings.address}
            onChange={(e) => setClubSettings({...clubSettings, address: e.target.value})}
            rows={3}
            className="input"
          />
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Logo et couleurs</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Logo du club
            </label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div className="space-y-1 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="flex text-sm text-gray-600">
                  <label className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500">
                    <span>Télécharger un fichier</span>
                    <input type="file" className="sr-only" accept="image/*" />
                  </label>
                </div>
                <p className="text-xs text-gray-500">PNG, JPG jusqu'à 2MB</p>
              </div>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Couleur principale
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={clubSettings.colors.primary}
                onChange={(e) => setClubSettings({
                  ...clubSettings, 
                  colors: {...clubSettings.colors, primary: e.target.value}
                })}
                className="h-10 w-20 rounded border border-gray-300"
              />
              <input
                type="text"
                value={clubSettings.colors.primary}
                onChange={(e) => setClubSettings({
                  ...clubSettings, 
                  colors: {...clubSettings.colors, primary: e.target.value}
                })}
                className="input flex-1"
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Couleur secondaire
            </label>
            <div className="flex items-center space-x-3">
              <input
                type="color"
                value={clubSettings.colors.secondary}
                onChange={(e) => setClubSettings({
                  ...clubSettings, 
                  colors: {...clubSettings.colors, secondary: e.target.value}
                })}
                className="h-10 w-20 rounded border border-gray-300"
              />
              <input
                type="text"
                value={clubSettings.colors.secondary}
                onChange={(e) => setClubSettings({
                  ...clubSettings, 
                  colors: {...clubSettings.colors, secondary: e.target.value}
                })}
                className="input flex-1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Paramètres de sécurité</h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900">Authentification à deux facteurs</h4>
              <p className="text-sm text-gray-500">Ajouter une couche de sécurité supplémentaire</p>
            </div>
            <button
              onClick={() => setSecuritySettings({
                ...securitySettings, 
                twoFactorAuth: !securitySettings.twoFactorAuth
              })}
              className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                securitySettings.twoFactorAuth ? 'bg-green-600' : 'bg-gray-200'
              }`}
            >
              <span
                className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                  securitySettings.twoFactorAuth ? 'translate-x-5' : 'translate-x-0'
                }`}
              />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Timeout de session (minutes)
              </label>
              <input
                type="number"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings, 
                  sessionTimeout: parseInt(e.target.value)
                })}
                className="input"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expiration mot de passe (jours)
              </label>
              <input
                type="number"
                value={securitySettings.passwordExpiry}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings, 
                  passwordExpiry: parseInt(e.target.value)
                })}
                className="input"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Tentatives de connexion max
              </label>
              <input
                type="number"
                value={securitySettings.loginAttempts}
                onChange={(e) => setSecuritySettings({
                  ...securitySettings, 
                  loginAttempts: parseInt(e.target.value)
                })}
                className="input"
              />
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Changer le mot de passe</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mot de passe actuel
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                className="input pr-10"
                placeholder="Entrez votre mot de passe actuel"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-400" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-400" />
                )}
              </button>
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Nouveau mot de passe
            </label>
            <input
              type="password"
              className="input"
              placeholder="Entrez le nouveau mot de passe"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Préférences de notification</h3>
        <div className="space-y-4">
          {Object.entries(notificationSettings).map(([key, value]) => {
            const labels = {
              emailNotifications: 'Notifications par email',
              matchReminders: 'Rappels de matchs',
              newsUpdates: 'Mises à jour des actualités',
              reservationAlerts: 'Alertes de réservation',
              systemAlerts: 'Alertes système'
            };
            
            return (
              <div key={key} className="flex items-center justify-between">
                <div>
                  <h4 className="text-sm font-medium text-gray-900">
                    {labels[key as keyof typeof labels]}
                  </h4>
                </div>
                <button
                  onClick={() => setNotificationSettings({
                    ...notificationSettings, 
                    [key]: !value
                  })}
                  className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 ${
                    value ? 'bg-green-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                      value ? 'translate-x-5' : 'translate-x-0'
                    }`}
                  />
                </button>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900">Paramètres</h1>
          <p className="mt-1 text-sm text-gray-600">
            Gérez les paramètres d'Espoir Sportif de Chorbane
          </p>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <nav className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      activeTab === tab.id
                        ? 'bg-green-100 text-green-700'
                        : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className="mr-3 h-5 w-5" />
                    {tab.name}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="flex-1">
            <div className="bg-white shadow rounded-lg p-6">
              {activeTab === 'club' && renderClubSettings()}
              {activeTab === 'security' && renderSecuritySettings()}
              {activeTab === 'notifications' && renderNotificationSettings()}
              {activeTab === 'profile' && (
                <div className="text-center py-12">
                  <User className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Paramètres de profil</h3>
                  <p className="mt-1 text-sm text-gray-500">Cette section sera bientôt disponible.</p>
                </div>
              )}
              {activeTab === 'appearance' && (
                <div className="text-center py-12">
                  <Palette className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Paramètres d'apparence</h3>
                  <p className="mt-1 text-sm text-gray-500">Cette section sera bientôt disponible.</p>
                </div>
              )}
              {activeTab === 'system' && (
                <div className="text-center py-12">
                  <Database className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">Paramètres système</h3>
                  <p className="mt-1 text-sm text-gray-500">Cette section sera bientôt disponible.</p>
                </div>
              )}

              {/* Save Button */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="flex justify-end">
                  <button
                    onClick={handleSave}
                    disabled={saving}
                    className="btn btn-primary"
                  >
                    {saving ? (
                      <div className="flex items-center">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Sauvegarde...
                      </div>
                    ) : (
                      <>
                        <Save className="h-5 w-5 mr-2" />
                        Sauvegarder
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default SettingsManagement;
