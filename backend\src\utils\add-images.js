const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// URLs d'images pour Espoir Sportive Chorbane
const clubImages = {
  // Logo et emblèmes
  logos: [
    'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400&h=400&fit=crop&crop=center', // Football générique
    'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center', // Équipe
    'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=400&h=400&fit=crop&crop=center', // Terrain
  ],

  // Photos d'équipe et joueurs
  team: [
    'https://images.unsplash.com/photo-1579952363873-27d3bfad9c0d?w=800&h=600&fit=crop&crop=center', // Équipe célébrant
    'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=800&h=600&fit=crop&crop=center', // Joueurs en action
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center', // Match
    'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=800&h=600&fit=crop&crop=center', // Entraînement
    'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center', // Supporters
  ],

  // Stade et installations
  stadium: [
    'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=1200&h=800&fit=crop&crop=center', // Stade
    'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=1200&h=800&fit=crop&crop=center', // Terrain vue aérienne
    'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=1200&h=800&fit=crop&crop=center', // Vestiaires
  ],

  // Actualités et événements
  news: [
    'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=600&h=400&fit=crop&crop=center', // Victoire
    'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=400&fit=crop&crop=center', // Nouveau joueur
    'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=600&h=400&fit=crop&crop=center', // Entraînement
    'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=600&h=400&fit=crop&crop=center', // Inauguration
  ],

  // Joueurs individuels (photos de profil)
  players: [
    'https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?w=300&h=400&fit=crop&crop=face', // Joueur 1
    'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=400&fit=crop&crop=face', // Joueur 2
    'https://images.unsplash.com/photo-1566577739112-5180d4bf9390?w=300&h=400&fit=crop&crop=face', // Joueur 3
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=400&fit=crop&crop=face', // Joueur 4
    'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=300&h=400&fit=crop&crop=face', // Joueur 5
    'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=300&h=400&fit=crop&crop=face', // Joueur 6
    'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=300&h=400&fit=crop&crop=face', // Joueur 7
    'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=300&h=400&fit=crop&crop=face', // Joueur 8
  ]
};

async function addImagesToDatabase() {
  try {
    console.log('🖼️  Ajout des images à la base de données...');

    // 1. Créer une galerie principale pour le club
    const mainGallery = await prisma.gallery.create({
      data: {
        title: 'Galerie Officielle - Espoir Sportive Chorbane',
        description: 'Collection officielle des photos du club : équipe, matchs, entraînements et événements',
        coverImage: clubImages.logos[0]
      }
    });

    console.log('✅ Galerie principale créée:', mainGallery.id);

    // 2. Ajouter les médias à la galerie
    const mediaItems = [];

    // Logos et emblèmes
    for (let i = 0; i < clubImages.logos.length; i++) {
      mediaItems.push({
        galleryId: mainGallery.id,
        fileName: `logo_${i + 1}.jpg`,
        filePath: clubImages.logos[i],
        fileType: 'image',
        fileSize: 150000 + Math.floor(Math.random() * 100000),
        mimeType: 'image/jpeg',
        title: `Logo Espoir Sportive Chorbane ${i + 1}`,
        description: 'Logo officiel du club',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // Photos d'équipe
    for (let i = 0; i < clubImages.team.length; i++) {
      mediaItems.push({
        galleryId: mainGallery.id,
        fileName: `team_${i + 1}.jpg`,
        filePath: clubImages.team[i],
        fileType: 'image',
        fileSize: 800000 + Math.floor(Math.random() * 200000),
        mimeType: 'image/jpeg',
        title: `Équipe Espoir Sportive ${i + 1}`,
        description: i === 0 ? 'Célébration après victoire' :
                    i === 1 ? 'Joueurs en action pendant le match' :
                    i === 2 ? 'Match officiel au stade' :
                    i === 3 ? 'Séance d\'entraînement' : 'Supporters du club',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // Photos du stade
    for (let i = 0; i < clubImages.stadium.length; i++) {
      mediaItems.push({
        galleryId: mainGallery.id,
        fileName: `stadium_${i + 1}.jpg`,
        filePath: clubImages.stadium[i],
        fileType: 'image',
        fileSize: 1200000 + Math.floor(Math.random() * 300000),
        mimeType: 'image/jpeg',
        title: `Stade Municipal Chorbane ${i + 1}`,
        description: i === 0 ? 'Vue générale du stade' :
                    i === 1 ? 'Terrain vue aérienne' : 'Installations du club',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // Insérer tous les médias
    await prisma.media.createMany({
      data: mediaItems
    });

    console.log(`✅ ${mediaItems.length} médias ajoutés à la galerie`);

    // 3. Mettre à jour les joueurs avec des photos
    const players = await prisma.player.findMany({
      take: clubImages.players.length
    });

    for (let i = 0; i < players.length && i < clubImages.players.length; i++) {
      await prisma.player.update({
        where: { id: players[i].id },
        data: {
          profileImage: clubImages.players[i]
        }
      });
    }

    console.log(`✅ ${Math.min(players.length, clubImages.players.length)} joueurs mis à jour avec des photos`);

    // 4. Mettre à jour les articles avec des images
    const articles = await prisma.article.findMany({
      take: clubImages.news.length
    });

    for (let i = 0; i < articles.length && i < clubImages.news.length; i++) {
      await prisma.article.update({
        where: { id: articles[i].id },
        data: {
          featuredImage: clubImages.news[i]
        }
      });
    }

    console.log(`✅ ${Math.min(articles.length, clubImages.news.length)} articles mis à jour avec des images`);

    // 5. Créer une galerie spéciale pour les matchs
    const matchGallery = await prisma.gallery.create({
      data: {
        title: 'Galerie des Matchs - Saison 2024/2025',
        description: 'Photos des matchs officiels de la saison en cours',
        isPublic: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // Ajouter quelques photos de matchs
    const matchMedia = [
      {
        galleryId: matchGallery.id,
        fileName: 'match_vs_soliman.jpg',
        filePath: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=1000&h=600&fit=crop',
        fileType: 'image',
        fileSize: 950000,
        mimeType: 'image/jpeg',
        title: 'ES Chorbane vs AS Soliman',
        description: 'Match de championnat - Victoire 3-1',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        galleryId: matchGallery.id,
        fileName: 'match_vs_bizertin.jpg',
        filePath: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=1000&h=600&fit=crop',
        fileType: 'image',
        fileSize: 920000,
        mimeType: 'image/jpeg',
        title: 'CA Bizertin vs ES Chorbane',
        description: 'Match à l\'extérieur - Victoire 3-1',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await prisma.media.createMany({
      data: matchMedia
    });

    console.log('✅ Galerie des matchs créée avec 2 photos');

    console.log('\n🎉 Images intégrées avec succès !');
    console.log(`📊 Résumé :`);
    console.log(`   • 1 galerie principale avec ${mediaItems.length} médias`);
    console.log(`   • 1 galerie de matchs avec ${matchMedia.length} photos`);
    console.log(`   • ${Math.min(players.length, clubImages.players.length)} joueurs avec photos`);
    console.log(`   • ${Math.min(articles.length, clubImages.news.length)} articles avec images`);

  } catch (error) {
    console.error('❌ Erreur lors de l\'ajout des images:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  addImagesToDatabase()
    .then(() => {
      console.log('✅ Script terminé avec succès');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

module.exports = { addImagesToDatabase, clubImages };
