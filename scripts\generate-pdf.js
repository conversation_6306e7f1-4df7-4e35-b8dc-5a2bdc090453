#!/usr/bin/env node

/**
 * Script pour générer un PDF à partir du fichier Markdown de conversation
 * Usage: node scripts/generate-pdf.js
 */

const fs = require('fs');
const path = require('path');

// Fonction pour créer un HTML stylé à partir du Markdown
function markdownToHtml(markdown) {
  // Simple conversion Markdown vers HTML
  let html = markdown
    // Headers
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    
    // Bold et italic
    .replace(/\*\*\*(.*?)\*\*\*/g, '<strong><em>$1</em></strong>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // Code blocks
    .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code class="language-$1">$2</code></pre>')
    .replace(/`([^`]+)`/g, '<code>$1</code>')
    
    // Links
    .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2">$1</a>')
    
    // Lists
    .replace(/^\- (.*$)/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/s, '<ul>$1</ul>')
    
    // Paragraphs
    .replace(/\n\n/g, '</p><p>')
    .replace(/^(?!<[h|u|p|d])(.+)$/gm, '<p>$1</p>')
    
    // Emojis et caractères spéciaux
    .replace(/---/g, '<hr>')
    .replace(/\n/g, '<br>');

  return html;
}

// Template HTML avec styles
function createHtmlTemplate(content) {
  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversation de Développement - Espoir Sportive Chorbane</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #ffffff;
            font-size: 14px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        h1 {
            font-family: 'Poppins', sans-serif;
            color: #0ea5e9;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 3px solid #facc15;
            padding-bottom: 10px;
        }
        
        h2 {
            font-family: 'Poppins', sans-serif;
            color: #1e40af;
            font-size: 20px;
            font-weight: 600;
            margin: 25px 0 15px 0;
            border-left: 4px solid #0ea5e9;
            padding-left: 15px;
        }
        
        h3 {
            font-family: 'Poppins', sans-serif;
            color: #374151;
            font-size: 16px;
            font-weight: 500;
            margin: 20px 0 10px 0;
        }
        
        p {
            margin-bottom: 12px;
            text-align: justify;
        }
        
        strong {
            color: #1f2937;
            font-weight: 600;
        }
        
        em {
            color: #6b7280;
            font-style: italic;
        }
        
        code {
            background: #f3f4f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            color: #dc2626;
        }
        
        pre {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            overflow-x: auto;
            font-size: 12px;
        }
        
        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }
        
        ul {
            margin: 10px 0 10px 20px;
        }
        
        li {
            margin-bottom: 5px;
        }
        
        hr {
            border: none;
            height: 2px;
            background: linear-gradient(to right, #0ea5e9, #facc15);
            margin: 30px 0;
        }
        
        a {
            color: #0ea5e9;
            text-decoration: none;
        }
        
        a:hover {
            text-decoration: underline;
        }
        
        .header-info {
            background: linear-gradient(135deg, #0ea5e9, #1e40af);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header-info p {
            margin: 5px 0;
            font-weight: 500;
        }
        
        .summary-box {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .success-box {
            background: #f0fdf4;
            border-left: 4px solid #22c55e;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning-box {
            background: #fffbeb;
            border-left: 4px solid #f59e0b;
            padding: 15px;
            margin: 15px 0;
        }
        
        .error-box {
            background: #fef2f2;
            border-left: 4px solid #ef4444;
            padding: 15px;
            margin: 15px 0;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            color: #6b7280;
            font-style: italic;
        }
        
        @media print {
            body { font-size: 12px; }
            .container { padding: 10px; }
            h1 { font-size: 24px; }
            h2 { font-size: 18px; }
            h3 { font-size: 14px; }
        }
    </style>
</head>
<body>
    <div class="container">
        ${content}
    </div>
</body>
</html>`;
}

// Fonction principale
async function generatePdf() {
  try {
    console.log('🔄 Génération du PDF de conversation...');
    
    // Lire le fichier Markdown
    const markdownPath = path.join(__dirname, '..', 'docs', 'CONVERSATION_CHAT.md');
    const markdownContent = fs.readFileSync(markdownPath, 'utf8');
    
    // Convertir en HTML
    const htmlContent = markdownToHtml(markdownContent);
    const fullHtml = createHtmlTemplate(htmlContent);
    
    // Sauvegarder le HTML temporaire
    const htmlPath = path.join(__dirname, '..', 'docs', 'conversation_temp.html');
    fs.writeFileSync(htmlPath, fullHtml);
    
    console.log('✅ Fichier HTML généré:', htmlPath);
    console.log('');
    console.log('📋 Pour générer le PDF:');
    console.log('1. Ouvrez le fichier HTML dans votre navigateur');
    console.log('2. Utilisez Ctrl+P (ou Cmd+P sur Mac)');
    console.log('3. Sélectionnez "Enregistrer au format PDF"');
    console.log('4. Sauvegardez sous: docs/CONVERSATION_CHAT.pdf');
    console.log('');
    console.log('🌐 Ou utilisez cette commande si vous avez wkhtmltopdf installé:');
    console.log(`wkhtmltopdf "${htmlPath}" "docs/CONVERSATION_CHAT.pdf"`);
    
  } catch (error) {
    console.error('❌ Erreur lors de la génération:', error.message);
    process.exit(1);
  }
}

// Exécuter le script
if (require.main === module) {
  generatePdf();
}

module.exports = { generatePdf, markdownToHtml, createHtmlTemplate };
