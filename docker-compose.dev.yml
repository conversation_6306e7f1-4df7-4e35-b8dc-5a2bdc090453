version: '3.8'

services:
  # Base de données PostgreSQL pour développement
  postgres-dev:
    image: postgres:15-alpine
    container_name: espoir-sportive-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: espoir_sportive_dev
      POSTGRES_USER: espoir_dev
      POSTGRES_PASSWORD: dev_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5433:5432"  # Port différent pour éviter les conflits
    networks:
      - espoir-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U espoir_dev -d espoir_sportive_dev"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend en mode développement avec hot reload
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: espoir-sportive-backend-dev
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: ******************************************************/espoir_sportive_dev?schema=public
      JWT_SECRET: dev-jwt-secret-key-for-development-only
      JWT_EXPIRES_IN: 24h
      PORT: 5000
      FRONTEND_URL: http://localhost:3000
      UPLOAD_DIR: uploads
      MAX_FILE_SIZE: 5242880
    volumes:
      - ./backend:/app
      - /app/node_modules
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    ports:
      - "5001:5000"  # Port différent pour éviter les conflits
    networks:
      - espoir-dev-network
    depends_on:
      postgres-dev:
        condition: service_healthy
    command: npm run dev

  # Frontend en mode développement avec hot reload
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: espoir-sportive-frontend-dev
    restart: unless-stopped
    environment:
      VITE_API_URL: http://localhost:5001/api
      VITE_APP_NAME: "Espoir Sportive Chorbane (Dev)"
      VITE_NODE_ENV: development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3001:3000"  # Port différent pour éviter les conflits
    networks:
      - espoir-dev-network
    depends_on:
      - backend-dev
    command: npm run dev

  # Adminer pour gestion base de données
  adminer-dev:
    image: adminer:latest
    container_name: espoir-sportive-adminer-dev
    restart: unless-stopped
    ports:
      - "8081:8080"
    networks:
      - espoir-dev-network
    depends_on:
      - postgres-dev
    environment:
      ADMINER_DEFAULT_SERVER: postgres-dev

  # Redis pour cache en développement
  redis-dev:
    image: redis:7-alpine
    container_name: espoir-sportive-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"
    networks:
      - espoir-dev-network

  # MailHog pour tester les emails en développement
  mailhog:
    image: mailhog/mailhog:latest
    container_name: espoir-sportive-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - espoir-dev-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  espoir-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
