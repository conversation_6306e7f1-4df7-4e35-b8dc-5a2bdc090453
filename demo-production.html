<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Espoir Sportif de Chorbane - Environnement de Production</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2d5a27 0%, #4a7c59 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }
        
        .logo {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }
        
        .status {
            display: inline-block;
            background: #28a745;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .features {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
        }
        
        .features h2 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .feature-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: #ffd700;
        }
        
        .accounts {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .accounts h3 {
            margin-bottom: 20px;
            color: #ffd700;
        }
        
        .account-item {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-family: monospace;
        }
        
        .tech-stack {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .tech-item {
            text-align: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            opacity: 0.8;
        }
        
        .btn {
            display: inline-block;
            background: #ffd700;
            color: #2d5a27;
            padding: 15px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            margin: 10px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #ffed4e;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🏆 ESC</div>
            <div class="subtitle">Espoir Sportif de Chorbane</div>
            <div class="status">🚀 ENVIRONNEMENT DE PRODUCTION ACTIF</div>
        </div>

        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">30</div>
                <div class="stat-label">Joueurs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">10</div>
                <div class="stat-label">Équipes</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">15</div>
                <div class="stat-label">Matchs</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">25</div>
                <div class="stat-label">Articles</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">3</div>
                <div class="stat-label">Administrateurs</div>
            </div>
        </div>

        <div class="features">
            <h2>🎯 Fonctionnalités Implémentées</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <span class="feature-icon">👤</span>
                    <span>Gestion complète des joueurs (CRUD)</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">✏️</span>
                    <span>Mise à jour des joueurs (UPDATE)</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🏆</span>
                    <span>Gestion des équipes tunisiennes</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">⚽</span>
                    <span>Système de matchs et résultats</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📰</span>
                    <span>Articles et actualités</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🔐</span>
                    <span>Authentification JWT</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📊</span>
                    <span>Statistiques des joueurs</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🖼️</span>
                    <span>Gestion des images</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">📱</span>
                    <span>Interface responsive</span>
                </div>
                <div class="feature-item">
                    <span class="feature-icon">🎨</span>
                    <span>Design inspiré du PSG</span>
                </div>
            </div>
        </div>

        <div class="accounts">
            <h3>🔑 Comptes de Test</h3>
            <div class="account-item">
                <strong>Admin 1:</strong> <EMAIL> / admin123
            </div>
            <div class="account-item">
                <strong>Admin 2:</strong> <EMAIL> / admin123
            </div>
            <div class="account-item">
                <strong>Admin 3:</strong> <EMAIL> / admin123
            </div>
            <div class="account-item">
                <strong>Joueurs:</strong> [prénom].[nom]@esc.tn / player123
            </div>
        </div>

        <div class="tech-stack">
            <h2>🛠️ Stack Technique</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <h4>Frontend</h4>
                    <p>React + TypeScript<br>Tailwind CSS<br>Vite</p>
                </div>
                <div class="tech-item">
                    <h4>Backend</h4>
                    <p>Node.js + Express<br>Prisma ORM<br>JWT Auth</p>
                </div>
                <div class="tech-item">
                    <h4>Base de Données</h4>
                    <p>SQLite (dev)<br>PostgreSQL (prod)<br>Migrations</p>
                </div>
                <div class="tech-item">
                    <h4>Sécurité</h4>
                    <p>Helmet.js<br>CORS<br>Rate Limiting</p>
                </div>
                <div class="tech-item">
                    <h4>Validation</h4>
                    <p>Express Validator<br>Prisma Schema<br>TypeScript</p>
                </div>
                <div class="tech-item">
                    <h4>Images</h4>
                    <p>Multer Upload<br>Static Serving<br>Unsplash API</p>
                </div>
            </div>
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <a href="http://localhost:3000" class="btn">🌐 Accéder au Frontend</a>
            <a href="http://localhost:5000/api/health" class="btn">📊 API Health Check</a>
            <a href="http://localhost:5000/api/players" class="btn">👥 API Joueurs</a>
        </div>

        <div class="footer">
            <p>🏆 Espoir Sportif de Chorbane - Système de Gestion d'Équipe</p>
            <p>Développé avec ❤️ pour la communauté sportive tunisienne</p>
            <p>© 2024 ESC - Tous droits réservés</p>
        </div>
    </div>

    <script>
        // Animation des statistiques
        function animateNumbers() {
            const numbers = document.querySelectorAll('.stat-number');
            numbers.forEach(num => {
                const target = parseInt(num.textContent);
                let current = 0;
                const increment = target / 50;
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    num.textContent = Math.floor(current);
                }, 50);
            });
        }

        // Vérification de l'état des services
        async function checkServices() {
            try {
                // Test du backend
                const backendResponse = await fetch('http://localhost:5000/api/health');
                if (backendResponse.ok) {
                    console.log('✅ Backend accessible');
                } else {
                    console.log('⚠️ Backend non accessible');
                }
            } catch (error) {
                console.log('❌ Backend non démarré');
            }

            try {
                // Test du frontend
                const frontendResponse = await fetch('http://localhost:3000');
                if (frontendResponse.ok) {
                    console.log('✅ Frontend accessible');
                } else {
                    console.log('⚠️ Frontend non accessible');
                }
            } catch (error) {
                console.log('❌ Frontend non démarré');
            }
        }

        // Démarrage des animations
        window.addEventListener('load', () => {
            animateNumbers();
            checkServices();
            console.log('🏆 Espoir Sportif de Chorbane - Environnement de Production');
            console.log('📊 Données générées avec Faker.js');
            console.log('🎯 Fonctionnalité de mise à jour des joueurs implémentée');
        });
    </script>
</body>
</html>
