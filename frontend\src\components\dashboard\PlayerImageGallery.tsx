import React, { useState } from 'react';
import { X, Plus, Edit, Trash2, Eye, Download } from 'lucide-react';
import ImageUpload from '../common/ImageUpload';

interface PlayerImage {
  id: string;
  url: string;
  caption?: string;
  isProfile: boolean;
  uploadedAt: string;
}

interface PlayerImageGalleryProps {
  playerId: string;
  images: PlayerImage[];
  onImagesChange: (images: PlayerImage[]) => void;
  maxImages?: number;
}

const PlayerImageGallery: React.FC<PlayerImageGalleryProps> = ({
  playerId,
  images,
  onImagesChange,
  maxImages = 10
}) => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState<PlayerImage | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [newImageCaption, setNewImageCaption] = useState('');

  const handleAddImage = (imageUrl: string | null) => {
    if (imageUrl) {
      const newImage: PlayerImage = {
        id: Date.now().toString(),
        url: imageUrl,
        caption: newImageCaption,
        isProfile: images.length === 0, // Première image = photo de profil
        uploadedAt: new Date().toISOString()
      };

      onImagesChange([...images, newImage]);
      setShowAddModal(false);
      setNewImageCaption('');
    }
  };

  const handleDeleteImage = (imageId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette image ?')) {
      const updatedImages = images.filter(img => img.id !== imageId);
      onImagesChange(updatedImages);
    }
  };

  const handleSetAsProfile = (imageId: string) => {
    const updatedImages = images.map(img => ({
      ...img,
      isProfile: img.id === imageId
    }));
    onImagesChange(updatedImages);
  };

  const handleUpdateCaption = (imageId: string, caption: string) => {
    const updatedImages = images.map(img =>
      img.id === imageId ? { ...img, caption } : img
    );
    onImagesChange(updatedImages);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Galerie d'images</h3>
          <p className="text-sm text-gray-600">
            {images.length}/{maxImages} images • Photo de profil marquée d'une étoile
          </p>
        </div>
        {images.length < maxImages && (
          <button
            onClick={() => setShowAddModal(true)}
            className="btn btn-outline btn-sm"
          >
            <Plus className="h-4 w-4 mr-2" />
            Ajouter une image
          </button>
        )}
      </div>

      {/* Images Grid */}
      {images.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square bg-gray-200 rounded-lg overflow-hidden">
                <img
                  src={image.url}
                  alt={image.caption || 'Photo du joueur'}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Profile Badge */}
              {image.isProfile && (
                <div className="absolute top-2 left-2">
                  <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                    ⭐ Profil
                  </span>
                </div>
              )}

              {/* Actions Overlay */}
              <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
                <div className="flex space-x-2">
                  <button
                    onClick={() => {
                      setSelectedImage(image);
                      setShowPreview(true);
                    }}
                    className="p-2 bg-white text-gray-700 rounded-full hover:bg-gray-100"
                    title="Voir en grand"
                  >
                    <Eye className="h-4 w-4" />
                  </button>
                  
                  {!image.isProfile && (
                    <button
                      onClick={() => handleSetAsProfile(image.id)}
                      className="p-2 bg-yellow-500 text-white rounded-full hover:bg-yellow-600"
                      title="Définir comme photo de profil"
                    >
                      ⭐
                    </button>
                  )}
                  
                  <button
                    onClick={() => handleDeleteImage(image.id)}
                    className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600"
                    title="Supprimer"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Caption */}
              {image.caption && (
                <div className="mt-2">
                  <p className="text-sm text-gray-600 truncate">{image.caption}</p>
                </div>
              )}

              {/* Upload Date */}
              <div className="mt-1">
                <p className="text-xs text-gray-500">{formatDate(image.uploadedAt)}</p>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="space-y-4">
            <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
              <Plus className="h-8 w-8 text-gray-400" />
            </div>
            <div>
              <h4 className="text-lg font-medium text-gray-900">Aucune image</h4>
              <p className="text-gray-600">Ajoutez des photos du joueur</p>
            </div>
            <button
              onClick={() => setShowAddModal(true)}
              className="btn btn-primary"
            >
              <Plus className="h-4 w-4 mr-2" />
              Ajouter la première image
            </button>
          </div>
        </div>
      )}

      {/* Add Image Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Ajouter une image</h3>
              <button
                onClick={() => setShowAddModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              <ImageUpload
                currentImage={null}
                onImageChange={handleAddImage}
                size="sm"
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Légende (optionnel)
                </label>
                <input
                  type="text"
                  value={newImageCaption}
                  onChange={(e) => setNewImageCaption(e.target.value)}
                  className="input"
                  placeholder="Décrivez cette image..."
                />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Preview Modal */}
      {showPreview && selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="max-w-4xl max-h-full p-4">
            <div className="relative">
              <img
                src={selectedImage.url}
                alt={selectedImage.caption || 'Photo du joueur'}
                className="max-w-full max-h-[80vh] object-contain rounded-lg"
              />
              
              <button
                onClick={() => setShowPreview(false)}
                className="absolute top-4 right-4 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-75"
              >
                <X className="h-5 w-5" />
              </button>

              {selectedImage.caption && (
                <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-50 text-white p-3 rounded">
                  <p className="text-sm">{selectedImage.caption}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlayerImageGallery;
