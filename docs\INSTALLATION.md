# Guide d'Installation - Espoir Sportive Chorbane

## 📋 Prérequis

### Logiciels Requis
- **Node.js** v18.0.0 ou supérieur
- **npm** v8.0.0 ou supérieur (inclus avec Node.js)
- **PostgreSQL** v13.0 ou supérieur
- **Git** pour le versioning

### Vérification des Prérequis
```bash
# Vérifier Node.js
node --version
# Doit afficher v18.x.x ou supérieur

# Vérifier npm
npm --version
# Doit afficher v8.x.x ou supérieur

# Vérifier PostgreSQL
psql --version
# Doit afficher PostgreSQL 13.x ou supérieur

# Vérifier Git
git --version
```

## 🚀 Installation Rapide

### 1. Cloner le Repository
```bash
git clone https://github.com/votre-username/espoir-sportive-chorbane.git
cd espoir-sportive-chorbane
```

### 2. Configuration de la Base de Données

#### Créer la Base de Données PostgreSQL
```bash
# Se connecter à PostgreSQL
psql -U postgres

# Créer la base de données
CREATE DATABASE espoir_sportive_db;

# Créer un utilisateur (optionnel)
CREATE USER espoir_user WITH PASSWORD 'votre_mot_de_passe';
GRANT ALL PRIVILEGES ON DATABASE espoir_sportive_db TO espoir_user;

# Quitter psql
\q
```

### 3. Installation Backend

```bash
# Aller dans le dossier backend
cd backend

# Installer les dépendances
npm install

# Copier le fichier d'environnement
cp .env.example .env
# Ou créer le fichier .env manuellement
```

#### Configuration du fichier .env
```bash
# Éditer le fichier .env
nano .env
```

Contenu du fichier `.env` :
```env
# Configuration de la base de données
DATABASE_URL="postgresql://postgres:votre_mot_de_passe@localhost:5432/espoir_sportive_db?schema=public"

# Configuration JWT
JWT_SECRET="votre-cle-secrete-jwt-tres-longue-et-complexe"
JWT_EXPIRES_IN="7d"

# Configuration du serveur
PORT=5000
NODE_ENV="development"

# Configuration CORS
FRONTEND_URL="http://localhost:3000"

# Configuration upload de fichiers
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=5242880

# Configuration email (optionnel)
EMAIL_HOST=""
EMAIL_PORT=""
EMAIL_USER=""
EMAIL_PASS=""
```

#### Initialisation de la Base de Données
```bash
# Générer le client Prisma
npm run db:generate

# Exécuter les migrations
npm run db:migrate

# Peupler la base avec des données de test
npm run db:seed
```

#### Démarrer le Backend
```bash
npm run dev
```

Le backend sera accessible sur `http://localhost:5000`

### 4. Installation Frontend

```bash
# Ouvrir un nouveau terminal et aller dans le dossier frontend
cd frontend

# Installer les dépendances
npm install

# Démarrer le frontend
npm run dev
```

Le frontend sera accessible sur `http://localhost:3000`

## 🔧 Configuration Avancée

### Variables d'Environnement Détaillées

#### Backend (.env)
```env
# Base de données
DATABASE_URL="postgresql://user:password@host:port/database"

# JWT
JWT_SECRET="cle-secrete-minimum-32-caracteres"
JWT_EXPIRES_IN="7d"  # 7 jours, 24h, 30m, etc.

# Serveur
PORT=5000
NODE_ENV="development"  # development, production, test

# CORS
FRONTEND_URL="http://localhost:3000"

# Upload
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=5242880  # 5MB en bytes

# Email (pour notifications futures)
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="votre-mot-de-passe-app"

# Logs
LOG_LEVEL="info"  # error, warn, info, debug
```

#### Frontend (.env.local)
```env
# API Backend
VITE_API_URL="http://localhost:5000/api"

# Application
VITE_APP_NAME="Espoir Sportive Chorbane"
VITE_APP_VERSION="1.0.0"

# Environnement
VITE_NODE_ENV="development"
```

### Configuration PostgreSQL

#### Installation PostgreSQL (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib

# Démarrer PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Se connecter
sudo -u postgres psql
```

#### Installation PostgreSQL (Windows)
1. Télécharger depuis https://www.postgresql.org/download/windows/
2. Exécuter l'installateur
3. Noter le mot de passe du superutilisateur
4. Ajouter PostgreSQL au PATH

#### Installation PostgreSQL (macOS)
```bash
# Avec Homebrew
brew install postgresql
brew services start postgresql

# Créer un utilisateur
createuser -s postgres
```

### Scripts Utiles

#### Backend
```bash
# Développement
npm run dev              # Démarrer avec hot reload

# Base de données
npm run db:migrate       # Exécuter migrations
npm run db:generate      # Générer client Prisma
npm run db:seed          # Peupler avec données test
npm run db:reset         # Reset complet + seed
npm run db:studio        # Interface graphique Prisma

# Production
npm start                # Démarrer en production
npm run db:deploy        # Déployer migrations en prod

# Maintenance
npm run clean            # Nettoyer node_modules
```

#### Frontend
```bash
# Développement
npm run dev              # Démarrer avec hot reload

# Build
npm run build            # Build pour production
npm run preview          # Prévisualiser le build

# Tests
npm run test             # Exécuter tests
npm run test:coverage    # Tests avec couverture

# Linting
npm run lint             # Vérifier le code
npm run lint:fix         # Corriger automatiquement
```

## 🧪 Vérification de l'Installation

### Tests Backend
```bash
cd backend

# Vérifier que le serveur démarre
npm run dev

# Tester l'API
curl http://localhost:5000/api/health
# Doit retourner : {"message":"API Espoir Sportive Chorbane fonctionne !"}

# Tester l'authentification
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'
```

### Tests Frontend
```bash
cd frontend

# Vérifier que l'application démarre
npm run dev

# Ouvrir http://localhost:3000 dans le navigateur
```

### Comptes de Test
Après le seed, vous pouvez vous connecter avec :
- **Admin** : `<EMAIL>` / `admin123`
- **Joueur** : `<EMAIL>` / `player123`
- **Supporter** : `<EMAIL>` / `supporter123`

## 🐛 Résolution de Problèmes

### Erreurs Communes

#### "Cannot connect to database"
```bash
# Vérifier que PostgreSQL fonctionne
sudo systemctl status postgresql

# Vérifier la connexion
psql -U postgres -d espoir_sportive_db

# Vérifier l'URL dans .env
echo $DATABASE_URL
```

#### "Port 5000 already in use"
```bash
# Trouver le processus utilisant le port
lsof -i :5000

# Tuer le processus
kill -9 <PID>

# Ou changer le port dans .env
PORT=5001
```

#### "Prisma Client not generated"
```bash
cd backend
npm run db:generate
```

#### "Migration failed"
```bash
# Reset complet de la base
npm run db:reset

# Ou migration manuelle
npx prisma migrate reset
npx prisma migrate dev
```

### Logs et Debugging

#### Backend
```bash
# Logs en temps réel
npm run logs

# Logs détaillés
NODE_ENV=development npm run dev
```

#### Frontend
```bash
# Console du navigateur (F12)
# Vérifier les erreurs réseau et JavaScript
```

## 📦 Installation avec Docker (Optionnel)

### Prérequis Docker
- Docker v20.0.0+
- Docker Compose v2.0.0+

### Démarrage avec Docker
```bash
# Construire et démarrer tous les services
docker-compose up -d

# Voir les logs
docker-compose logs -f

# Arrêter les services
docker-compose down
```

## 🔄 Mise à Jour

### Mise à jour du Code
```bash
# Récupérer les dernières modifications
git pull origin main

# Mettre à jour les dépendances backend
cd backend
npm install

# Mettre à jour les dépendances frontend
cd ../frontend
npm install

# Exécuter les nouvelles migrations
cd ../backend
npm run db:migrate
```

### Sauvegarde avant Mise à Jour
```bash
# Sauvegarder la base de données
pg_dump espoir_sportive_db > backup_$(date +%Y%m%d_%H%M%S).sql

# Sauvegarder les uploads
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz backend/uploads/
```

---

*Guide d'installation mis à jour le 24/05/2025*
