# Espoir Sportif de Chorbane - Système de Gestion

Application web complète pour la gestion d'un club de football tunisien avec un design moderne inspiré du Paris Saint-Germain.

## 📋 Journal de Développement

### Phase 1 : Architecture et Configuration Backend ✅
**Date : 24/05/2025**

#### 🔧 Configuration initiale
- ✅ Initialisation du projet Node.js/Express
- ✅ Configuration Prisma ORM avec PostgreSQL
- ✅ Setup des variables d'environnement (.env)
- ✅ Structure des dossiers backend organisée

#### 🗄️ Base de données
- ✅ Schéma Prisma complet avec 15+ modèles
- ✅ Relations complexes entre entités
- ✅ Énumérations pour rôles, positions, statuts
- ✅ Modèles : User, Player, Match, Training, Article, Gallery, etc.

#### 🔐 Authentification et Sécurité
- ✅ Middleware JWT complet
- ✅ Hashage bcrypt des mots de passe
- ✅ Validation des mots de passe forts
- ✅ Gestion des rôles (ADMIN, PLAYER, SUPPORTER)
- ✅ Protection des routes par rôles

#### 🛣️ API Routes Développées
- ✅ `/api/auth` - Authentification (login, register, verify)
- ✅ `/api/users` - Gestion utilisateurs CRUD
- ✅ `/api/players` - Gestion joueurs avec statistiques
- ✅ `/api/matches` - Gestion matchs et compositions
- ✅ `/api/trainings` - Gestion entraînements et présences
- ✅ `/api/articles` - Système de blog/actualités
- ✅ `/api/galleries` - Gestion galeries multimédia
- ✅ `/api/stats` - Statistiques équipe et joueurs

#### 🌱 Données de test avec Faker.js
- ✅ Script de seed complet avec Faker.js
- ✅ 28 joueurs avec noms tunisiens réalistes et statistiques
- ✅ 25 matchs avec scores réalistes et distribution authentique
- ✅ 30 entraînements avec présences (88% de taux de présence)
- ✅ 25 supporters avec profils variés
- ✅ Articles générés avec titres contextuels
- ✅ Commentaires et événements de match
- ✅ Scripts modulaires pour générer des données spécifiques

### Phase 2 : Configuration Docker ✅
**Date : 24/05/2025**

#### 🐳 Docker et Déploiement
- ✅ Docker Compose pour développement et production
- ✅ Dockerfiles optimisés (multi-stage builds)
- ✅ Configuration Nginx avec reverse proxy
- ✅ Scripts de démarrage automatisés (Bash + PowerShell)
- ✅ Volumes persistants pour données
- ✅ Health checks et monitoring
- ✅ Configuration SSL pour production
- ✅ Services optionnels (Redis, Adminer, MailHog)

### Phase 3 : Frontend React (En cours)
**Date : 24/05/2025**

#### 🎨 Configuration Frontend
- ✅ Projet React créé avec Vite + TypeScript
- ⏳ Installation des dépendances (Tailwind, Router, etc.)
- ⏳ Configuration Tailwind CSS
- ⏳ Structure des composants

## 🎯 Fonctionnalités Implémentées

### 👥 Gestion des utilisateurs
- **Administrateur** : Gestion complète de l'équipe
- **Joueurs** : Consultation des infos, planning, statistiques
- **Supporters** : Suivi de l'actualité du club

### ⚽ Fonctionnalités Backend Complètes
- ✅ **Gestion des joueurs** : CRUD complet avec profils détaillés
- ✅ **Planning des matchs** : Création, modification, compositions
- ✅ **Entraînements** : Planification et suivi des présences
- ✅ **Statistiques** : Calculs automatiques équipe/joueurs
- ✅ **Blog/Actualités** : Système de publication avec tags
- ✅ **Galerie multimédia** : Upload et organisation des médias
- ✅ **Authentification** : Système complet avec JWT

### 🐳 Infrastructure Docker
- ✅ **Docker Compose** : Développement et production
- ✅ **Multi-services** : Backend, Frontend, DB, Cache, Proxy
- ✅ **Hot Reload** : Développement avec rechargement automatique
- ✅ **Health Checks** : Monitoring automatique des services
- ✅ **Volumes persistants** : Données et logs sauvegardés
- ✅ **SSL/HTTPS** : Configuration production sécurisée

### 🎭 Données Réalistes avec Faker.js
- ✅ **Noms tunisiens** : 30+ prénoms et noms authentiques
- ✅ **Clubs réels** : 18 clubs de football tunisiens
- ✅ **Statistiques réalistes** : Adaptées par position de joueur
- ✅ **Scores authentiques** : Distribution basée sur vraies statistiques
- ✅ **Génération modulaire** : Scripts pour données spécifiques
- ✅ **Données cohérentes** : Emails, téléphones, dates réalistes

### 📊 Statistiques Avancées
- ✅ Statistiques équipe (victoires, défaites, buts)
- ✅ Statistiques individuelles par saison
- ✅ Taux de présence aux entraînements
- ✅ Historique des performances
- ✅ Tableaux de bord personnalisés

## 🛠️ Technologies

### Backend (Implémenté)
- **Node.js** v18+ avec Express.js
- **PostgreSQL** avec Prisma ORM
- **JWT** pour l'authentification
- **bcryptjs** pour le hashage des mots de passe
- **express-validator** pour la validation
- **multer** pour l'upload de fichiers
- **helmet** et **cors** pour la sécurité

### Frontend (En cours)
- **React** v18 avec Vite
- **TypeScript** pour le typage
- **Tailwind CSS** pour le design responsive
- **React Router** pour la navigation
- **Axios** pour les appels API
- **Chart.js** pour les graphiques

### DevOps
- **Git** pour le versioning
- **ESLint** et **Prettier** pour la qualité du code
- **Docker** avec Docker Compose
- **Nginx** pour reverse proxy
- **Scripts** automatisés (Bash + PowerShell)

## 🚀 Installation et démarrage

### Prérequis
- Node.js (v18+)
- PostgreSQL
- Git

### Installation Backend
```bash
# Cloner le projet
git clone <repository-url>
cd espoir-sportive-chorbane

# Installation des dépendances backend
cd backend
npm install

# Configuration de la base de données
cp .env.example .env
# Modifier DATABASE_URL dans .env

# Migrations et génération Prisma
npx prisma migrate dev
npx prisma generate

# Seed de la base de données (basique)
npm run db:seed

# Seed avancé avec Faker.js (recommandé)
npm run db:seed-advanced

# Générer des données spécifiques
npm run db:generate-data articles 10    # 10 articles
npm run db:generate-data matches 5      # 5 matchs
npm run db:generate-data supporters 20  # 20 supporters
```

### Démarrage Backend
```bash
cd backend
npm run dev
# API disponible sur http://localhost:5000
```

### Installation Frontend
```bash
cd frontend
npm install
npm run dev
# Application disponible sur http://localhost:3000
```

## 🐳 Démarrage avec Docker (Recommandé)

### Prérequis Docker
- Docker v20.0.0+
- Docker Compose v2.0.0+

### Démarrage Rapide

#### Linux/macOS
```bash
# Rendre le script exécutable
chmod +x scripts/docker-start.sh

# Démarrer en développement
./scripts/docker-start.sh dev

# Démarrer en production
./scripts/docker-start.sh prod
```

#### Windows (PowerShell)
```bash
# Démarrer en développement
.\scripts\docker-start.ps1 dev

# Démarrer en production
.\scripts\docker-start.ps1 prod
```

#### Avec Make (Linux/macOS)
```bash
# Configuration initiale
make setup

# Démarrage
make dev                    # Développement
make prod                   # Production
make stop                   # Arrêter tous les services

# Base de données
make db-seed-advanced       # Seed avec Faker (recommandé)
make db-reset-advanced      # Reset complet avec Faker
make db-generate-articles   # Générer 10 articles
make db-generate-matches    # Générer 5 matchs

# Logs et monitoring
make logs                   # Voir les logs
make status                 # Statut des services
make health                 # Vérifier la santé

# Maintenance
make clean                  # Nettoyage léger
make docker-build          # Reconstruire les images
```

### Services Disponibles

#### Développement
- **Frontend** : http://localhost:3001
- **Backend API** : http://localhost:5001
- **Adminer (DB)** : http://localhost:8081
- **MailHog** : http://localhost:8025

#### Production
- **Application** : http://localhost:3000
- **API** : http://localhost:5000

## 📁 Structure du projet
```
espoir-sportive-chorbane/
├── README.md                  # Documentation principale
├── backend/                   # API Node.js/Express
│   ├── src/
│   │   ├── config/           # Configuration DB
│   │   ├── middleware/       # Middlewares auth/validation
│   │   ├── routes/           # Routes API (8 modules)
│   │   ├── utils/            # Utilitaires (auth, seed)
│   │   └── server.js         # Point d'entrée
│   ├── prisma/
│   │   └── schema.prisma     # Schéma base de données
│   ├── package.json
│   └── .env                  # Variables d'environnement
├── frontend/                 # Application React
│   ├── src/
│   │   ├── components/       # Composants réutilisables
│   │   ├── pages/           # Pages de l'application
│   │   ├── hooks/           # Hooks personnalisés
│   │   ├── services/        # Services API
│   │   └── utils/           # Utilitaires
│   ├── package.json
│   └── vite.config.ts       # Configuration Vite
└── docs/                    # Documentation technique
```

## 🔐 Authentification et rôles

### Comptes de test (après seed)
- **Admin** : `<EMAIL>` / `admin123`
- **Joueur** : `<EMAIL>` / `player123`
- **Supporter** : `<EMAIL>` / `supporter123`

### Permissions par rôle
- **ADMIN** : Accès complet (CRUD sur toutes les entités)
- **PLAYER** : Lecture des données équipe + modification profil personnel
- **SUPPORTER** : Lecture des données publiques uniquement

## 📱 API Endpoints

### Authentification
- `POST /api/auth/register` - Inscription
- `POST /api/auth/login` - Connexion
- `GET /api/auth/me` - Profil utilisateur
- `GET /api/auth/verify` - Vérification token

### Gestion des données
- `GET/POST/PUT/DELETE /api/users` - Utilisateurs
- `GET/POST/PUT/DELETE /api/players` - Joueurs
- `GET/POST/PUT/DELETE /api/matches` - Matchs
- `GET/POST/PUT/DELETE /api/trainings` - Entraînements
- `GET/POST/PUT/DELETE /api/articles` - Articles
- `GET/POST/PUT/DELETE /api/galleries` - Galeries

### Statistiques
- `GET /api/stats/team` - Stats équipe
- `GET /api/stats/player/:id` - Stats joueur
- `GET /api/stats/dashboard` - Tableau de bord

## 🧪 Tests et Validation

### Validation des données
- ✅ Validation express-validator sur toutes les routes
- ✅ Sanitisation des entrées utilisateur
- ✅ Gestion d'erreurs centralisée
- ✅ Messages d'erreur en français

### Sécurité
- ✅ Rate limiting (100 req/15min)
- ✅ Helmet.js pour les headers sécurisés
- ✅ CORS configuré
- ✅ Validation JWT sur routes protégées

## 🚀 Prochaines étapes

### Phase 3 : Frontend React (En cours)
1. ⏳ Configuration complète Tailwind CSS
2. ⏳ Composants de base (Header, Sidebar, Cards)
3. ⏳ Pages principales (Dashboard, Joueurs, Matchs)
4. ⏳ Système d'authentification frontend
5. ⏳ Intégration API backend

### Phase 4 : Fonctionnalités avancées
1. ⏳ Upload d'images avec preview
2. ⏳ Graphiques statistiques (Chart.js)
3. ⏳ Notifications en temps réel
4. ⏳ Export PDF/Excel
5. ⏳ PWA (Progressive Web App)

### Phase 5 : CI/CD et Monitoring
1. ⏳ CI/CD avec GitHub Actions
2. ⏳ Tests automatisés (Jest, Cypress)
3. ⏳ Monitoring et logs (Prometheus, Grafana)
4. ⏳ Déploiement production automatisé

## 📝 Commits et Versioning

### Convention de commits
- `feat:` Nouvelle fonctionnalité
- `fix:` Correction de bug
- `docs:` Documentation
- `style:` Formatage, style
- `refactor:` Refactoring
- `test:` Tests
- `chore:` Maintenance

### Branches
- `main` : Production
- `develop` : Développement
- `feature/*` : Nouvelles fonctionnalités

## 📞 Support et Contact
- **Développeur** : Hamza Bedoui
- **Email** : <EMAIL>
- **Projet** : Espoir Sportive Chorbane

---
⚽ **Espoir Sportive Chorbane** - Gérez votre équipe comme un pro !

*Dernière mise à jour : 24/05/2025*
