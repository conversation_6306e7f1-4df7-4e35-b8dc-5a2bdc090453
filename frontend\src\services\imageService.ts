// Service pour gérer les uploads d'images
// Dans une vraie application, ceci communiquerait avec votre backend

export interface UploadResponse {
  success: boolean;
  imageUrl?: string;
  error?: string;
}

export class ImageService {
  private static instance: ImageService;
  private uploadedImages: Map<string, string> = new Map();

  private constructor() {}

  public static getInstance(): ImageService {
    if (!ImageService.instance) {
      ImageService.instance = new ImageService();
    }
    return ImageService.instance;
  }

  /**
   * Simule l'upload d'une image vers le serveur
   * Dans une vraie application, ceci ferait un appel API
   */
  public async uploadImage(file: File): Promise<UploadResponse> {
    try {
      // Validation du fichier
      const validationError = this.validateFile(file);
      if (validationError) {
        return { success: false, error: validationError };
      }

      // Simulation d'un délai d'upload
      await this.delay(1000 + Math.random() * 2000);

      // Simulation d'un échec occasionnel (5% de chance)
      if (Math.random() < 0.05) {
        return { success: false, error: 'Erreur réseau lors du téléchargement' };
      }

      // Génération d'un nom de fichier unique
      const fileName = this.generateFileName(file);
      
      // Création d'une URL locale pour la démo
      // Dans une vraie app, ce serait l'URL retournée par le serveur
      const imageUrl = URL.createObjectURL(file);
      
      // Stockage local pour la démo
      this.uploadedImages.set(fileName, imageUrl);

      // Simulation d'un upload vers un service cloud
      // const formData = new FormData();
      // formData.append('image', file);
      // const response = await fetch('/api/upload', {
      //   method: 'POST',
      //   body: formData
      // });
      // const result = await response.json();

      return {
        success: true,
        imageUrl: imageUrl
      };

    } catch (error) {
      console.error('Erreur lors de l\'upload:', error);
      return {
        success: false,
        error: 'Erreur inattendue lors du téléchargement'
      };
    }
  }

  /**
   * Supprime une image du serveur
   */
  public async deleteImage(imageUrl: string): Promise<boolean> {
    try {
      // Simulation d'un délai
      await this.delay(500);

      // Dans une vraie app, appel API pour supprimer l'image
      // await fetch(`/api/images/${imageId}`, { method: 'DELETE' });

      // Nettoyage local
      URL.revokeObjectURL(imageUrl);
      
      // Suppression du stockage local
      for (const [key, url] of this.uploadedImages.entries()) {
        if (url === imageUrl) {
          this.uploadedImages.delete(key);
          break;
        }
      }

      return true;
    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      return false;
    }
  }

  /**
   * Redimensionne une image avant l'upload
   */
  public async resizeImage(file: File, maxWidth: number = 800, maxHeight: number = 800, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calcul des nouvelles dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        // Redimensionnement
        canvas.width = width;
        canvas.height = height;
        
        ctx?.drawImage(img, 0, 0, width, height);

        // Conversion en blob puis en file
        canvas.toBlob((blob) => {
          if (blob) {
            const resizedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(resizedFile);
          } else {
            resolve(file);
          }
        }, file.type, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Valide un fichier image
   */
  private validateFile(file: File): string | null {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return 'Format de fichier non supporté. Utilisez JPG, PNG ou WebP.';
    }

    if (file.size > maxSize) {
      return 'Fichier trop volumineux. Taille maximum: 5MB.';
    }

    return null;
  }

  /**
   * Génère un nom de fichier unique
   */
  private generateFileName(file: File): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2);
    const extension = file.name.split('.').pop();
    return `player_${timestamp}_${random}.${extension}`;
  }

  /**
   * Utilitaire pour simuler un délai
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Obtient les informations d'une image
   */
  public getImageInfo(file: File): Promise<{width: number, height: number, size: number}> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({
          width: img.naturalWidth,
          height: img.naturalHeight,
          size: file.size
        });
      };
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Convertit une image en base64
   */
  public fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }
}

// Export de l'instance singleton
export const imageService = ImageService.getInstance();
