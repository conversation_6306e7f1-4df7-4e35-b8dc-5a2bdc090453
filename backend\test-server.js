const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware de base
app.use(cors());
app.use(express.json());

// Route de test
app.get('/api/health', (req, res) => {
  res.json({
    message: 'API Espoir Sportive Chorbane fonctionne !',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Démarrage du serveur
app.listen(PORT, () => {
  console.log(`🚀 Serveur de test démarré sur le port ${PORT}`);
  console.log(`🌍 Environnement: ${process.env.NODE_ENV}`);
  console.log(`📊 API disponible sur: http://localhost:${PORT}/api`);
});

module.exports = app;
