import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider, ProtectedRoute } from './contexts/AuthContext';
import Header from './components/Header';
import Footer from './components/Footer';
import HomePage from './pages/HomePage';
import PlayersPage from './pages/PlayersPage';
import MatchesPage from './pages/MatchesPage';
import NewsPage from './pages/NewsPage';
import ContactPage from './pages/ContactPage';
import ReservationPage from './pages/ReservationPage';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import PlayersManagement from './pages/dashboard/PlayersManagement';
import MatchesManagement from './pages/dashboard/MatchesManagement';
import NewsManagement from './pages/dashboard/NewsManagement';
import ReservationsManagement from './pages/dashboard/ReservationsManagement';
import StatsManagement from './pages/dashboard/StatsManagement';
import SettingsManagement from './pages/dashboard/SettingsManagement';
import AddPlayer from './pages/dashboard/AddPlayer';
import AddMatch from './pages/dashboard/AddMatch';
import AddArticle from './pages/dashboard/AddArticle';
import AddReservation from './pages/dashboard/AddReservation';
import './styles.css';

function App() {
  return (
    <AuthProvider>
      <Router future={{ v7_relativeSplatPath: true }}>
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />

          {/* Main site routes */}
          <Route path="/*" element={
            <div className="min-h-screen flex flex-col">
              <Header />
              <main className="flex-grow">
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/players" element={<PlayersPage />} />
                  <Route path="/matches" element={<MatchesPage />} />
                  <Route path="/news" element={<NewsPage />} />
                  <Route path="/contact" element={<ContactPage />} />
                  <Route path="/reservations" element={<ReservationPage />} />
                </Routes>
              </main>
              <Footer />
            </div>
          } />

          {/* Dashboard routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <DashboardPage />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/players" element={
            <ProtectedRoute requiredRole="ADMIN">
              <PlayersManagement />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/matches" element={
            <ProtectedRoute requiredRole="ADMIN">
              <MatchesManagement />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/news" element={
            <ProtectedRoute requiredRole="ADMIN">
              <NewsManagement />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/reservations" element={
            <ProtectedRoute requiredRole="ADMIN">
              <ReservationsManagement />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/stats" element={
            <ProtectedRoute requiredRole="ADMIN">
              <StatsManagement />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/settings" element={
            <ProtectedRoute requiredRole="ADMIN">
              <SettingsManagement />
            </ProtectedRoute>
          } />

          {/* Add/Edit Routes */}
          <Route path="/dashboard/players/add" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddPlayer />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/players/edit/:id" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddPlayer />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/matches/add" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddMatch />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/matches/edit/:id" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddMatch />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/news/add" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddArticle />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/news/edit/:id" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddArticle />
            </ProtectedRoute>
          } />
          <Route path="/dashboard/reservations/add" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AddReservation />
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
