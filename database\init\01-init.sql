-- Script d'initialisation PostgreSQL pour Espoir Sportive Chorbane
-- Ce script est exécuté automatiquement lors de la création du conteneur PostgreSQL

-- Créer des extensions utiles
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "unaccent";

-- Créer un utilisateur pour l'application (si pas déjà fait par les variables d'environnement)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'espoir_user') THEN
        CREATE USER espoir_user WITH PASSWORD 'espoir_password';
    END IF;
END
$$;

-- Accorder les privilèges nécessaires
GRANT ALL PRIVILEGES ON DATABASE espoir_sportive_db TO espoir_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO espoir_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO espoir_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO espoir_user;

-- Créer des index pour améliorer les performances (sera fait par Prisma mais on peut préparer)
-- Ces commandes seront exécutées après que Prisma ait créé les tables

-- Configuration PostgreSQL pour optimiser les performances
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET pg_stat_statements.track = 'all';

-- Paramètres de performance pour une petite à moyenne application
ALTER SYSTEM SET max_connections = 100;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Recharger la configuration
SELECT pg_reload_conf();

-- Créer un schéma pour les logs (optionnel)
CREATE SCHEMA IF NOT EXISTS logs;
GRANT ALL PRIVILEGES ON SCHEMA logs TO espoir_user;

-- Table pour les logs d'audit (optionnel)
CREATE TABLE IF NOT EXISTS logs.audit_log (
    id SERIAL PRIMARY KEY,
    table_name VARCHAR(255) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_data JSONB,
    new_data JSONB,
    user_id VARCHAR(255),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index pour les logs
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON logs.audit_log(table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON logs.audit_log(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON logs.audit_log(user_id);

-- Fonction pour nettoyer les anciens logs (garder 90 jours)
CREATE OR REPLACE FUNCTION logs.cleanup_old_logs()
RETURNS void AS $$
BEGIN
    DELETE FROM logs.audit_log 
    WHERE timestamp < NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Créer un utilisateur en lecture seule pour les rapports (optionnel)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'espoir_readonly') THEN
        CREATE USER espoir_readonly WITH PASSWORD 'readonly_password';
    END IF;
END
$$;

-- Accorder les privilèges de lecture seule
GRANT CONNECT ON DATABASE espoir_sportive_db TO espoir_readonly;
GRANT USAGE ON SCHEMA public TO espoir_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO espoir_readonly;

-- Assurer que les futurs tables auront aussi les bons privilèges
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO espoir_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO espoir_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO espoir_readonly;

-- Message de confirmation
DO $$
BEGIN
    RAISE NOTICE 'Base de données Espoir Sportive Chorbane initialisée avec succès !';
    RAISE NOTICE 'Extensions créées : uuid-ossp, pg_trgm, unaccent';
    RAISE NOTICE 'Utilisateurs créés : espoir_user (full access), espoir_readonly (read-only)';
    RAISE NOTICE 'Schéma logs créé pour l''audit';
END
$$;
