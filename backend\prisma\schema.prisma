// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Énumération des rôles utilisateur
enum UserRole {
  ADMIN
  PLAYER
  SUPPORTER
}

// Énumération des postes de joueur
enum PlayerPosition {
  GOALKEEPER
  DEFENDER
  MIDFIELDER
  FORWARD
}

// Énumération des statuts de match
enum MatchStatus {
  SCHEDULED
  LIVE
  FINISHED
  CANCELLED
  POSTPONED
}

// Énumération des types de carton
enum CardType {
  YELLOW
  RED
}

// Modèle Utilisateur
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  role      UserRole @default(SUPPORTER)
  avatar    String?
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  player   Player?
  articles Article[]
  comments Comment[]

  @@map("users")
}

// Modèle Joueur
model Player {
  id              String         @id @default(cuid())
  userId          String         @unique
  jerseyNumber    Int            @unique
  position        PlayerPosition
  height          Float?
  weight          Float?
  dateOfBirth     DateTime?
  nationality     String?
  preferredFoot   PreferredFoot?
  contractStart   DateTime?
  contractEnd     DateTime?
  salary          Float?
  isActive        Boolean        @default(true)
  biography       String?
  previousClubs   String?
  achievements    String?
  profileImage    String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  user            User              @relation(fields: [userId], references: [id], onDelete: Cascade)
  matchPlayers    MatchPlayer[]
  playerStats     PlayerStats[]
  trainingAttendances TrainingAttendance[]

  @@map("players")
}

// Ancien modèle Match supprimé - remplacé par le nouveau modèle Team/Match

// Modèle de liaison Match-Joueur
model MatchPlayer {
  id       String  @id @default(cuid())
  matchId  String
  playerId String
  isStarter Boolean @default(false)
  minutesPlayed Int @default(0)

  // Relations
  match  Match  @relation(fields: [matchId], references: [id], onDelete: Cascade)
  player Player @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([matchId, playerId])
  @@map("match_players")
}

// Modèle Événement de Match
model MatchEvent {
  id       String    @id @default(cuid())
  matchId  String
  playerId String?
  minute   Int
  type     String    // GOAL, CARD, SUBSTITUTION, etc.
  cardType CardType?
  description String?

  // Relations
  match Match @relation(fields: [matchId], references: [id], onDelete: Cascade)

  @@map("match_events")
}

// Modèle Statistiques Joueur
model PlayerStats {
  id           String @id @default(cuid())
  playerId     String
  season       String @default("2024-2025")
  matchesPlayed Int   @default(0)
  goals        Int    @default(0)
  assists      Int    @default(0)
  yellowCards  Int    @default(0)
  redCards     Int    @default(0)
  minutesPlayed Int   @default(0)

  // Relations
  player Player @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([playerId, season])
  @@map("player_stats")
}

// Modèle Entraînement
model Training {
  id          String   @id @default(cuid())
  title       String
  description String?
  date        DateTime
  location    String
  duration    Int      // en minutes
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  attendances TrainingAttendance[]

  @@map("trainings")
}

// Modèle Présence Entraînement
model TrainingAttendance {
  id         String  @id @default(cuid())
  trainingId String
  playerId   String
  isPresent  Boolean @default(false)
  notes      String?

  // Relations
  training Training @relation(fields: [trainingId], references: [id], onDelete: Cascade)
  player   Player   @relation(fields: [playerId], references: [id], onDelete: Cascade)

  @@unique([trainingId, playerId])
  @@map("training_attendances")
}

// Modèle Article/Actualité
model Article {
  id          String   @id @default(cuid())
  title       String
  content     String
  excerpt     String?
  coverImage  String?
  featuredImage String?
  isPublished Boolean  @default(false)
  authorId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  author   User      @relation(fields: [authorId], references: [id])
  comments Comment[]
  tags     ArticleTag[]

  @@map("articles")
}

// Modèle Commentaire
model Comment {
  id        String   @id @default(cuid())
  content   String
  articleId String
  authorId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  author  User    @relation(fields: [authorId], references: [id])

  @@map("comments")
}

// Modèle Tag
model Tag {
  id   String @id @default(cuid())
  name String @unique

  // Relations
  articles ArticleTag[]

  @@map("tags")
}

// Modèle de liaison Article-Tag
model ArticleTag {
  articleId String
  tagId     String

  // Relations
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([articleId, tagId])
  @@map("article_tags")
}

// Modèle Équipe
model Team {
  id          String @id @default(cuid())
  name        String @unique
  logo        String?
  city        String?
  stadium     String?
  foundedYear Int?
  description String?
  website     String?
  isActive    Boolean @default(true)

  // Relations
  homeMatches Match[] @relation("HomeTeam")
  awayMatches Match[] @relation("AwayTeam")

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("teams")
}

// Modèle Match
model Match {
  id          String @id @default(cuid())
  homeTeamId  String
  awayTeamId  String
  homeTeam    Team   @relation("HomeTeam", fields: [homeTeamId], references: [id])
  awayTeam    Team   @relation("AwayTeam", fields: [awayTeamId], references: [id])

  matchDate   DateTime
  venue       String?
  homeScore   Int?
  awayScore   Int?
  status      MatchStatus @default(SCHEDULED)
  season      String
  competition String?
  description String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("matches")
}

// Modèle News (simplifié pour le seed)
model News {
  id            String @id @default(cuid())
  title         String
  content       String
  excerpt       String?
  featuredImage String?
  category      String?
  tags          String?
  isPublished   Boolean @default(false)
  publishedAt   DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("news")
}

// Modèle Galerie
model Gallery {
  id          String   @id @default(cuid())
  title       String
  description String?
  coverImage  String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  media GalleryMedia[]

  @@map("galleries")
}

// Modèle Média de Galerie
model GalleryMedia {
  id        String   @id @default(cuid())
  galleryId String
  fileName  String
  filePath  String
  fileType  String   // image, video
  caption   String?
  createdAt DateTime @default(now())

  // Relations
  gallery Gallery @relation(fields: [galleryId], references: [id], onDelete: Cascade)

  @@map("gallery_media")
}

// Énumérations
enum MatchStatus {
  SCHEDULED
  LIVE
  FINISHED
  CANCELLED
  POSTPONED
}

enum PreferredFoot {
  LEFT
  RIGHT
  BOTH
}
