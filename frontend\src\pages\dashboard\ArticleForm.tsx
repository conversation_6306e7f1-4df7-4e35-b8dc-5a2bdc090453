import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Save,
  ArrowLeft,
  Upload,
  Eye,
  Tag,
  Calendar,
  User,
  FileText,
  Image,
  Globe
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface ArticleFormData {
  title: string;
  content: string;
  excerpt: string;
  category: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  featuredImage?: string;
  tags: string[];
  publishedAt?: string;
  metaDescription: string;
  slug: string;
}

const ArticleForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = Boolean(id);

  const [formData, setFormData] = useState<ArticleFormData>({
    title: '',
    content: '',
    excerpt: '',
    category: 'MATCH',
    status: 'DRAFT',
    tags: [],
    metaDescription: '',
    slug: ''
  });

  const [newTag, setNewTag] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  const categories = [
    { value: 'MATCH', label: 'Match', color: 'bg-blue-100 text-blue-800' },
    { value: 'TRANSFERT', label: 'Transfert', color: 'bg-purple-100 text-purple-800' },
    { value: 'ENTRAINEMENT', label: 'Entraînement', color: 'bg-orange-100 text-orange-800' },
    { value: 'CLUB', label: 'Club', color: 'bg-green-100 text-green-800' },
    { value: 'COMMUNAUTE', label: 'Communauté', color: 'bg-pink-100 text-pink-800' }
  ];

  useEffect(() => {
    if (isEditing) {
      setLoading(true);
      setTimeout(() => {
        // Mock data for editing
        setFormData({
          title: 'Victoire éclatante contre le CA Bizertin',
          content: `# Une performance exceptionnelle

L'équipe d'Espoir Sportif de Chorbane a livré une performance remarquable lors du match contre le CA Bizertin, s'imposant 3-1 dans un match spectaculaire.

## Les moments forts

- **15e minute** : Ouverture du score par Hamza Bedoui sur une magnifique action collective
- **32e minute** : Égalisation de Bizertin sur penalty
- **58e minute** : Remise en avant grâce à un coup franc de Mohamed Sassi
- **78e minute** : But du break signé Ahmed Khalil

## Réactions d'après-match

L'entraîneur s'est montré très satisfait de la performance de ses joueurs, soulignant l'esprit d'équipe et la détermination affichée tout au long de la rencontre.

Les supporters présents au stade ont également salué cette belle victoire qui permet au club de consolider sa position au classement.`,
          excerpt: 'Une performance exceptionnelle de nos joueurs lors du dernier match contre le CA Bizertin.',
          category: 'MATCH',
          status: 'PUBLISHED',
          tags: ['match', 'victoire', 'championnat', 'CA Bizertin'],
          publishedAt: '2025-05-20T10:00:00',
          metaDescription: 'Espoir Sportif de Chorbane s\'impose 3-1 face au CA Bizertin dans un match spectaculaire.',
          slug: 'victoire-eclatante-contre-ca-bizertin'
        });
        setLoading(false);
      }, 1000);
    }
  }, [isEditing]);

  const handleChange = (field: keyof ArticleFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-generate slug from title
    if (field === 'title') {
      const slug = value
        .toLowerCase()
        .replace(/[àáâãäå]/g, 'a')
        .replace(/[èéêë]/g, 'e')
        .replace(/[ìíîï]/g, 'i')
        .replace(/[òóôõö]/g, 'o')
        .replace(/[ùúûü]/g, 'u')
        .replace(/[ç]/g, 'c')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Set published date if publishing
      const dataToSave = {
        ...formData,
        publishedAt: formData.status === 'PUBLISHED' && !formData.publishedAt 
          ? new Date().toISOString() 
          : formData.publishedAt
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert(isEditing ? 'Article modifié avec succès !' : 'Article créé avec succès !');
      navigate('/dashboard/news');
    } catch (error) {
      alert('Erreur lors de la sauvegarde');
    } finally {
      setSaving(false);
    }
  };

  const getCategoryInfo = (categoryValue: string) => {
    return categories.find(cat => cat.value === categoryValue) || categories[0];
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard/news')}
                className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {isEditing ? 'Modifier l\'article' : 'Nouvel article'}
                </h1>
                <p className="mt-1 text-sm text-gray-600">
                  {isEditing ? 'Modifiez le contenu de l\'article' : 'Créez un nouvel article pour le site'}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => setPreviewMode(!previewMode)}
                className="btn btn-outline"
              >
                <Eye className="h-4 w-4 mr-2" />
                {previewMode ? 'Éditer' : 'Aperçu'}
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Title */}
              <div className="bg-white shadow rounded-lg p-6">
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Titre de l'article *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleChange('title', e.target.value)}
                    className="input text-lg font-medium"
                    placeholder="Entrez le titre de l'article..."
                    required
                  />
                </div>

                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    URL (slug)
                  </label>
                  <div className="flex items-center">
                    <span className="text-sm text-gray-500 mr-2">
                      /articles/
                    </span>
                    <input
                      type="text"
                      value={formData.slug}
                      onChange={(e) => handleChange('slug', e.target.value)}
                      className="input flex-1"
                      placeholder="url-de-l-article"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Résumé *
                  </label>
                  <textarea
                    value={formData.excerpt}
                    onChange={(e) => handleChange('excerpt', e.target.value)}
                    rows={3}
                    className="input"
                    placeholder="Résumé de l'article qui apparaîtra dans les listes..."
                    required
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    {formData.excerpt.length}/200 caractères
                  </p>
                </div>
              </div>

              {/* Content */}
              <div className="bg-white shadow rounded-lg p-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Contenu de l'article *
                </label>
                {previewMode ? (
                  <div className="border rounded-lg p-4 min-h-96 bg-gray-50">
                    <div className="prose max-w-none">
                      {formData.content.split('\n').map((paragraph, index) => {
                        if (paragraph.startsWith('# ')) {
                          return <h1 key={index} className="text-2xl font-bold mb-4">{paragraph.slice(2)}</h1>;
                        }
                        if (paragraph.startsWith('## ')) {
                          return <h2 key={index} className="text-xl font-semibold mb-3">{paragraph.slice(3)}</h2>;
                        }
                        if (paragraph.startsWith('- **')) {
                          const match = paragraph.match(/- \*\*(.*?)\*\* : (.*)/);
                          if (match) {
                            return (
                              <li key={index} className="mb-2">
                                <strong>{match[1]}</strong> : {match[2]}
                              </li>
                            );
                          }
                        }
                        if (paragraph.trim()) {
                          return <p key={index} className="mb-4">{paragraph}</p>;
                        }
                        return <br key={index} />;
                      })}
                    </div>
                  </div>
                ) : (
                  <textarea
                    value={formData.content}
                    onChange={(e) => handleChange('content', e.target.value)}
                    rows={20}
                    className="input font-mono text-sm"
                    placeholder="Rédigez le contenu de l'article en Markdown..."
                    required
                  />
                )}
                <p className="mt-2 text-sm text-gray-500">
                  Vous pouvez utiliser Markdown pour formater le texte (# pour les titres, ** pour le gras, etc.)
                </p>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publication Settings */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Publication</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Statut
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) => handleChange('status', e.target.value)}
                      className="input"
                    >
                      <option value="DRAFT">Brouillon</option>
                      <option value="PUBLISHED">Publié</option>
                      <option value="ARCHIVED">Archivé</option>
                    </select>
                  </div>

                  {formData.status === 'PUBLISHED' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Date de publication
                      </label>
                      <input
                        type="datetime-local"
                        value={formData.publishedAt?.slice(0, 16) || ''}
                        onChange={(e) => handleChange('publishedAt', e.target.value)}
                        className="input"
                      />
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Catégorie
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleChange('category', e.target.value)}
                      className="input"
                    >
                      {categories.map(category => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                    <div className="mt-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryInfo(formData.category).color}`}>
                        {getCategoryInfo(formData.category).label}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Featured Image */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Image à la une</h3>
                <div className="space-y-4">
                  {formData.featuredImage ? (
                    <div>
                      <img
                        src={formData.featuredImage}
                        alt="Image à la une"
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => handleChange('featuredImage', '')}
                        className="mt-2 text-sm text-red-600 hover:text-red-800"
                      >
                        Supprimer l'image
                      </button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Image className="mx-auto h-12 w-12 text-gray-400" />
                      <div className="mt-2">
                        <button
                          type="button"
                          className="btn btn-outline btn-sm"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Ajouter une image
                        </button>
                      </div>
                      <p className="mt-2 text-xs text-gray-500">
                        JPG, PNG jusqu'à 2MB
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Tags */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                      className="input flex-1"
                      placeholder="Ajouter un tag..."
                    />
                    <button
                      type="button"
                      onClick={handleAddTag}
                      className="btn btn-outline btn-sm"
                    >
                      <Tag className="h-4 w-4" />
                    </button>
                  </div>
                  
                  {formData.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tag)}
                            className="ml-1 text-gray-400 hover:text-gray-600"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* SEO */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">SEO</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Meta description
                  </label>
                  <textarea
                    value={formData.metaDescription}
                    onChange={(e) => handleChange('metaDescription', e.target.value)}
                    rows={3}
                    className="input"
                    placeholder="Description pour les moteurs de recherche..."
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    {formData.metaDescription.length}/160 caractères
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pb-8">
            <button
              type="button"
              onClick={() => navigate('/dashboard/news')}
              className="btn btn-outline"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sauvegarde...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditing ? 'Modifier' : 'Créer'} l'article
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default ArticleForm;
