const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

const prisma = new PrismaClient();

// Images pour Espoir Sportif de Chorbane avec recherche Google
const clubImages = {
  // Logos et emblèmes du club
  logos: [
    {
      url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=400&h=400&fit=crop&crop=center',
      filename: 'logo_principal.jpg',
      title: 'Logo Principal Espoir Sportif de Chorbane',
      description: 'Logo officiel du club'
    },
    {
      url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center',
      filename: 'embleme_club.jpg',
      title: 'Emblème du Club',
      description: 'Emblème traditionnel ESC Chorbane'
    },
    {
      url: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=400&h=400&fit=crop&crop=center',
      filename: 'logo_moderne.jpg',
      title: 'Logo Moderne',
      description: 'Version moderne du logo'
    }
  ],

  // Photos d'équipe et matchs
  team: [
    {
      url: 'https://images.unsplash.com/photo-1579952363873-27d3bfad9c0d?w=800&h=600&fit=crop&crop=center',
      filename: 'equipe_celebration.jpg',
      title: 'Célébration de l\'équipe',
      description: 'L\'équipe célèbre une victoire importante'
    },
    {
      url: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=800&h=600&fit=crop&crop=center',
      filename: 'match_action.jpg',
      title: 'Action de match',
      description: 'Joueurs en action pendant un match officiel'
    },
    {
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center',
      filename: 'match_domicile.jpg',
      title: 'Match à domicile',
      description: 'Match au stade municipal de Chorbane'
    },
    {
      url: 'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=800&h=600&fit=crop&crop=center',
      filename: 'entrainement_equipe.jpg',
      title: 'Entraînement de l\'équipe',
      description: 'Séance d\'entraînement préparatoire'
    },
    {
      url: 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center',
      filename: 'supporters_chorbane.jpg',
      title: 'Supporters d\'Espoir Sportif',
      description: 'Les fidèles supporters d\'Espoir Sportif de Chorbane'
    }
  ],

  // Stade et installations
  stadium: [
    {
      url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=1200&h=800&fit=crop&crop=center',
      filename: 'stade_municipal_chorbane.jpg',
      title: 'Stade Municipal de Chorbane',
      description: 'Vue générale du stade municipal'
    },
    {
      url: 'https://images.unsplash.com/photo-1508098682722-e99c43a406b2?w=1200&h=800&fit=crop&crop=center',
      filename: 'terrain_aerien.jpg',
      title: 'Vue aérienne du terrain',
      description: 'Le terrain vu du ciel'
    },
    {
      url: 'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=1200&h=800&fit=crop&crop=center',
      filename: 'installations_club.jpg',
      title: 'Installations du club',
      description: 'Vestiaires et installations sportives'
    }
  ],

  // Photos pour actualités
  news: [
    {
      url: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=600&h=400&fit=crop&crop=center',
      filename: 'victoire_importante.jpg',
      title: 'Victoire importante',
      description: 'Célébration après une victoire décisive'
    },
    {
      url: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=600&h=400&fit=crop&crop=center',
      filename: 'nouveau_joueur.jpg',
      title: 'Nouveau joueur',
      description: 'Présentation d\'un nouveau joueur'
    },
    {
      url: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=600&h=400&fit=crop&crop=center',
      filename: 'preparation_saison.jpg',
      title: 'Préparation de saison',
      description: 'Entraînement intensif pour la nouvelle saison'
    },
    {
      url: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=600&h=400&fit=crop&crop=center',
      filename: 'inauguration_academie.jpg',
      title: 'Inauguration de l\'académie',
      description: 'Ouverture du nouveau centre de formation'
    }
  ],

  // Photos de joueurs
  players: [
    {
      url: 'https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?w=300&h=400&fit=crop&crop=face',
      filename: 'joueur_1.jpg',
      title: 'Ahmed Ben Ali',
      description: 'Gardien de but titulaire'
    },
    {
      url: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300&h=400&fit=crop&crop=face',
      filename: 'joueur_2.jpg',
      title: 'Mohamed Trabelsi',
      description: 'Défenseur central'
    },
    {
      url: 'https://images.unsplash.com/photo-1566577739112-5180d4bf9390?w=300&h=400&fit=crop&crop=face',
      filename: 'joueur_3.jpg',
      title: 'Youssef Hamdi',
      description: 'Défenseur latéral'
    },
    {
      url: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=400&fit=crop&crop=face',
      filename: 'joueur_4.jpg',
      title: 'Karim Sassi',
      description: 'Milieu de terrain'
    },
    {
      url: 'https://images.unsplash.com/photo-1560272564-c83b66b1ad12?w=300&h=400&fit=crop&crop=face',
      filename: 'joueur_5.jpg',
      title: 'Hamza Bedoui',
      description: 'Attaquant vedette'
    },
    {
      url: 'https://images.unsplash.com/photo-1577223625816-7546f13df25d?w=300&h=400&fit=crop&crop=face',
      filename: 'joueur_6.jpg',
      title: 'Montassar Meriah',
      description: 'Défenseur expérimenté'
    }
  ]
};

// Fonction pour télécharger une image
function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https:') ? https : http;

    const file = fs.createWriteStream(filepath);

    protocol.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image: ${response.statusCode}`));
        return;
      }

      response.pipe(file);

      file.on('finish', () => {
        file.close();
        resolve(filepath);
      });

      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Supprimer le fichier en cas d'erreur
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Fonction pour créer les dossiers nécessaires
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Fonction principale pour télécharger et sauvegarder les images
async function downloadAndSaveImages() {
  try {
    console.log('🖼️  Téléchargement et sauvegarde des images...');

    // Créer les dossiers nécessaires
    const baseDir = path.join(__dirname, '..', '..', 'public', 'images');
    ensureDirectoryExists(path.join(baseDir, 'club'));
    ensureDirectoryExists(path.join(baseDir, 'players'));
    ensureDirectoryExists(path.join(baseDir, 'matches'));
    ensureDirectoryExists(path.join(baseDir, 'news'));

    let totalDownloaded = 0;

    // 1. Télécharger les logos
    console.log('📥 Téléchargement des logos...');
    for (const logo of clubImages.logos) {
      const filepath = path.join(baseDir, 'club', logo.filename);
      try {
        await downloadImage(logo.url, filepath);
        console.log(`✅ Logo téléchargé: ${logo.filename}`);
        totalDownloaded++;
      } catch (error) {
        console.error(`❌ Erreur téléchargement ${logo.filename}:`, error.message);
      }
    }

    // 2. Télécharger les photos d'équipe
    console.log('📥 Téléchargement des photos d\'équipe...');
    for (const teamPhoto of clubImages.team) {
      const filepath = path.join(baseDir, 'club', teamPhoto.filename);
      try {
        await downloadImage(teamPhoto.url, filepath);
        console.log(`✅ Photo équipe téléchargée: ${teamPhoto.filename}`);
        totalDownloaded++;
      } catch (error) {
        console.error(`❌ Erreur téléchargement ${teamPhoto.filename}:`, error.message);
      }
    }

    // 3. Télécharger les photos du stade
    console.log('📥 Téléchargement des photos du stade...');
    for (const stadiumPhoto of clubImages.stadium) {
      const filepath = path.join(baseDir, 'club', stadiumPhoto.filename);
      try {
        await downloadImage(stadiumPhoto.url, filepath);
        console.log(`✅ Photo stade téléchargée: ${stadiumPhoto.filename}`);
        totalDownloaded++;
      } catch (error) {
        console.error(`❌ Erreur téléchargement ${stadiumPhoto.filename}:`, error.message);
      }
    }

    // 4. Télécharger les photos pour actualités
    console.log('📥 Téléchargement des photos d\'actualités...');
    for (const newsPhoto of clubImages.news) {
      const filepath = path.join(baseDir, 'news', newsPhoto.filename);
      try {
        await downloadImage(newsPhoto.url, filepath);
        console.log(`✅ Photo actualité téléchargée: ${newsPhoto.filename}`);
        totalDownloaded++;
      } catch (error) {
        console.error(`❌ Erreur téléchargement ${newsPhoto.filename}:`, error.message);
      }
    }

    // 5. Télécharger les photos de joueurs
    console.log('📥 Téléchargement des photos de joueurs...');
    for (const playerPhoto of clubImages.players) {
      const filepath = path.join(baseDir, 'players', playerPhoto.filename);
      try {
        await downloadImage(playerPhoto.url, filepath);
        console.log(`✅ Photo joueur téléchargée: ${playerPhoto.filename}`);
        totalDownloaded++;
      } catch (error) {
        console.error(`❌ Erreur téléchargement ${playerPhoto.filename}:`, error.message);
      }
    }

    console.log(`\n🎉 ${totalDownloaded} images téléchargées avec succès !`);

    // 6. Maintenant sauvegarder les liens dans la base de données
    await saveImageLinksToDatabase();

  } catch (error) {
    console.error('❌ Erreur lors du téléchargement des images:', error);
    throw error;
  }
}

// Fonction pour sauvegarder les liens dans la base de données
async function saveImageLinksToDatabase() {
  try {
    console.log('\n💾 Sauvegarde des liens dans la base de données...');

    const baseUrl = 'http://localhost:5001/images';

    // 1. Créer une galerie principale
    const mainGallery = await prisma.gallery.create({
      data: {
        title: 'Galerie Officielle - Espoir Sportif de Chorbane',
        description: 'Collection officielle des photos du club : équipe, matchs, entraînements et événements',
        coverImage: `${baseUrl}/club/logo_principal.jpg`
      }
    });

    console.log('✅ Galerie principale créée');

    // 2. Ajouter les médias à la galerie
    const mediaItems = [];

    // Logos
    clubImages.logos.forEach((logo, index) => {
      mediaItems.push({
        galleryId: mainGallery.id,
        fileName: logo.filename,
        filePath: `${baseUrl}/club/${logo.filename}`,
        fileType: 'image',
        caption: logo.description
      });
    });

    // Photos d'équipe
    clubImages.team.forEach((photo, index) => {
      mediaItems.push({
        galleryId: mainGallery.id,
        fileName: photo.filename,
        filePath: `${baseUrl}/club/${photo.filename}`,
        fileType: 'image',
        caption: photo.description
      });
    });

    // Photos du stade
    clubImages.stadium.forEach((photo, index) => {
      mediaItems.push({
        galleryId: mainGallery.id,
        fileName: photo.filename,
        filePath: `${baseUrl}/club/${photo.filename}`,
        fileType: 'image',
        caption: photo.description
      });
    });

    // Insérer tous les médias
    await prisma.galleryMedia.createMany({
      data: mediaItems
    });

    console.log(`✅ ${mediaItems.length} médias ajoutés à la galerie`);

    // 3. Mettre à jour les joueurs avec des photos
    const players = await prisma.player.findMany({
      take: clubImages.players.length
    });

    for (let i = 0; i < players.length && i < clubImages.players.length; i++) {
      await prisma.player.update({
        where: { id: players[i].id },
        data: {
          profileImage: `${baseUrl}/players/${clubImages.players[i].filename}`
        }
      });
    }

    console.log(`✅ ${Math.min(players.length, clubImages.players.length)} joueurs mis à jour avec des photos`);

    // 4. Mettre à jour les articles avec des images
    const articles = await prisma.article.findMany({
      take: clubImages.news.length
    });

    for (let i = 0; i < articles.length && i < clubImages.news.length; i++) {
      await prisma.article.update({
        where: { id: articles[i].id },
        data: {
          featuredImage: `${baseUrl}/news/${clubImages.news[i].filename}`
        }
      });
    }

    console.log(`✅ ${Math.min(articles.length, clubImages.news.length)} articles mis à jour avec des images`);

    console.log('\n🎉 Sauvegarde terminée avec succès !');

  } catch (error) {
    console.error('❌ Erreur lors de la sauvegarde en base:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  downloadAndSaveImages()
    .then(() => {
      console.log('✅ Script terminé avec succès');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

module.exports = { downloadAndSaveImages, clubImages };
