const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../config/database');
const { authenticateToken, requireAdmin, requirePlayerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /api/players - Récupérer tous les joueurs
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 20, position, isActive = 'true', search } = req.query;
    const skip = (page - 1) * limit;

    // Construire les filtres
    const where = {};
    if (position) {
      where.position = position;
    }
    if (isActive !== 'all') {
      where.isActive = isActive === 'true';
    }
    if (search) {
      where.user = {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' } },
          { lastName: { contains: search, mode: 'insensitive' } }
        ]
      };
    }

    const [players, total] = await Promise.all([
      prisma.player.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
              avatar: true,
              phone: true
            }
          },
          playerStats: {
            where: {
              season: '2024-2025'
            }
          }
        },
        orderBy: {
          jerseyNumber: 'asc'
        }
      }),
      prisma.player.count({ where })
    ]);

    res.json({
      message: 'Joueurs récupérés avec succès',
      players,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des joueurs:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/players/:id - Récupérer un joueur par ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const player = await prisma.player.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
            phone: true,
            createdAt: true
          }
        },
        playerStats: {
          orderBy: {
            season: 'desc'
          }
        },
        matchPlayers: {
          include: {
            match: {
              select: {
                id: true,
                homeTeam: true,
                awayTeam: true,
                matchDate: true,
                homeScore: true,
                awayScore: true,
                status: true
              }
            }
          },
          orderBy: {
            match: {
              matchDate: 'desc'
            }
          },
          take: 10
        }
      }
    });

    if (!player) {
      return res.status(404).json({
        message: 'Joueur non trouvé'
      });
    }

    res.json({
      message: 'Joueur récupéré avec succès',
      player
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du joueur:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/players - Créer un nouveau joueur (Admin seulement)
router.post('/', [
  body('userId')
    .notEmpty()
    .withMessage('ID utilisateur requis'),
  body('jerseyNumber')
    .isInt({ min: 1, max: 99 })
    .withMessage('Numéro de maillot invalide (1-99)'),
  body('position')
    .isIn(['GOALKEEPER', 'DEFENDER', 'MIDFIELDER', 'FORWARD'])
    .withMessage('Position invalide'),
  body('height')
    .optional()
    .isFloat({ min: 1.0, max: 2.5 })
    .withMessage('Taille invalide'),
  body('weight')
    .optional()
    .isFloat({ min: 30, max: 200 })
    .withMessage('Poids invalide'),
  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('Date de naissance invalide'),
  body('nationality')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Nationalité invalide')
], requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { 
      userId, 
      jerseyNumber, 
      position, 
      height, 
      weight, 
      birthDate, 
      nationality, 
      biography 
    } = req.body;

    // Vérifier que l'utilisateur existe et n'est pas déjà un joueur
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { player: true }
    });

    if (!user) {
      return res.status(404).json({
        message: 'Utilisateur non trouvé'
      });
    }

    if (user.player) {
      return res.status(409).json({
        message: 'Cet utilisateur est déjà un joueur'
      });
    }

    // Vérifier l'unicité du numéro de maillot
    const existingPlayer = await prisma.player.findUnique({
      where: { jerseyNumber }
    });

    if (existingPlayer) {
      return res.status(409).json({
        message: 'Ce numéro de maillot est déjà utilisé'
      });
    }

    // Créer le joueur
    const player = await prisma.player.create({
      data: {
        userId,
        jerseyNumber,
        position,
        height,
        weight,
        birthDate: birthDate ? new Date(birthDate) : null,
        nationality,
        biography
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
            phone: true
          }
        }
      }
    });

    // Mettre à jour le rôle de l'utilisateur
    await prisma.user.update({
      where: { id: userId },
      data: { role: 'PLAYER' }
    });

    // Créer les statistiques initiales pour la saison actuelle
    await prisma.playerStats.create({
      data: {
        playerId: player.id,
        season: '2024-2025'
      }
    });

    res.status(201).json({
      message: 'Joueur créé avec succès',
      player
    });

  } catch (error) {
    console.error('Erreur lors de la création du joueur:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// PUT /api/players/:id - Mettre à jour un joueur
router.put('/:id', [
  body('jerseyNumber')
    .optional()
    .isInt({ min: 1, max: 99 })
    .withMessage('Numéro de maillot invalide (1-99)'),
  body('position')
    .optional()
    .isIn(['GOALKEEPER', 'DEFENDER', 'MIDFIELDER', 'FORWARD'])
    .withMessage('Position invalide'),
  body('height')
    .optional()
    .isFloat({ min: 1.0, max: 2.5 })
    .withMessage('Taille invalide'),
  body('weight')
    .optional()
    .isFloat({ min: 30, max: 200 })
    .withMessage('Poids invalide'),
  body('birthDate')
    .optional()
    .isISO8601()
    .withMessage('Date de naissance invalide'),
  body('nationality')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Nationalité invalide'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('Statut actif invalide')
], requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { 
      jerseyNumber, 
      position, 
      height, 
      weight, 
      birthDate, 
      nationality, 
      biography,
      isActive
    } = req.body;

    // Vérifier que le joueur existe
    const existingPlayer = await prisma.player.findUnique({
      where: { id }
    });

    if (!existingPlayer) {
      return res.status(404).json({
        message: 'Joueur non trouvé'
      });
    }

    // Vérifier l'unicité du numéro de maillot si modifié
    if (jerseyNumber && jerseyNumber !== existingPlayer.jerseyNumber) {
      const playerWithNumber = await prisma.player.findUnique({
        where: { jerseyNumber }
      });
      if (playerWithNumber) {
        return res.status(409).json({
          message: 'Ce numéro de maillot est déjà utilisé'
        });
      }
    }

    const updatedPlayer = await prisma.player.update({
      where: { id },
      data: {
        jerseyNumber,
        position,
        height,
        weight,
        birthDate: birthDate ? new Date(birthDate) : undefined,
        nationality,
        biography,
        isActive
      },
      include: {
        user: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            avatar: true,
            phone: true
          }
        },
        playerStats: {
          where: {
            season: '2024-2025'
          }
        }
      }
    });

    res.json({
      message: 'Joueur mis à jour avec succès',
      player: updatedPlayer
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du joueur:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
