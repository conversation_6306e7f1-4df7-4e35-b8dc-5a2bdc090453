import React, { useState, useRef } from 'react';
import { Upload, X, Camera, User, AlertCircle } from 'lucide-react';
import { imageService } from '../../services/imageService';

interface ImageUploadProps {
  currentImage?: string;
  onImageChange: (imageUrl: string | null) => void;
  placeholder?: string;
  maxSize?: number; // in MB
  acceptedFormats?: string[];
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  currentImage,
  onImageChange,
  placeholder = "Aucune image sélectionnée",
  maxSize = 2,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  className = '',
  size = 'md'
}) => {
  const [dragOver, setDragOver] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sizeClasses = {
    sm: 'w-24 h-24',
    md: 'w-32 h-32',
    lg: 'w-48 h-48'
  };

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!acceptedFormats.includes(file.type)) {
      return `Format non supporté. Utilisez: ${acceptedFormats.map(f => f.split('/')[1]).join(', ')}`;
    }

    // Check file size
    const fileSizeMB = file.size / (1024 * 1024);
    if (fileSizeMB > maxSize) {
      return `Fichier trop volumineux. Taille maximum: ${maxSize}MB`;
    }

    return null;
  };

  const handleFileUpload = async (file: File) => {
    setError(null);

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setUploading(true);

    try {
      // Redimensionner l'image si nécessaire
      const resizedFile = await imageService.resizeImage(file, 800, 800, 0.8);

      // Upload de l'image
      const result = await imageService.uploadImage(resizedFile);

      if (result.success && result.imageUrl) {
        onImageChange(result.imageUrl);
      } else {
        setError(result.error || 'Erreur lors du téléchargement');
      }
    } catch (err) {
      setError('Erreur lors du téléchargement de l\'image');
      console.error('Upload error:', err);
    } finally {
      setUploading(false);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setDragOver(false);
  };

  const handleRemoveImage = async () => {
    if (currentImage) {
      // Supprimer l'image du serveur
      await imageService.deleteImage(currentImage);
    }

    onImageChange(null);
    setError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Image Preview */}
      <div className="flex items-center space-x-6">
        <div className={`${sizeClasses[size]} relative flex-shrink-0`}>
          {currentImage ? (
            <div className="relative group">
              <img
                src={currentImage}
                alt="Photo du joueur"
                className={`${sizeClasses[size]} rounded-lg object-cover border-2 border-gray-200`}
              />
              {/* Overlay on hover */}
              <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <button
                  type="button"
                  onClick={handleRemoveImage}
                  className="p-2 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors"
                  title="Supprimer l'image"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          ) : (
            <div className={`${sizeClasses[size]} bg-gray-200 rounded-lg flex items-center justify-center border-2 border-dashed border-gray-300`}>
              <User className="h-8 w-8 text-gray-400" />
            </div>
          )}
        </div>

        {/* Upload Area */}
        <div className="flex-1">
          <div
            className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
              dragOver
                ? 'border-green-400 bg-green-50'
                : 'border-gray-300 hover:border-gray-400'
            } ${uploading ? 'opacity-50 pointer-events-none' : ''}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {uploading ? (
              <div className="space-y-2">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                <p className="text-sm text-gray-600">Téléchargement en cours...</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="flex justify-center">
                  {currentImage ? (
                    <Camera className="h-8 w-8 text-gray-400" />
                  ) : (
                    <Upload className="h-8 w-8 text-gray-400" />
                  )}
                </div>

                <div>
                  <button
                    type="button"
                    onClick={openFileDialog}
                    className="btn btn-outline btn-sm"
                  >
                    {currentImage ? 'Changer la photo' : 'Télécharger une photo'}
                  </button>
                  <p className="mt-2 text-xs text-gray-500">
                    ou glissez-déposez une image ici
                  </p>
                </div>

                <div className="text-xs text-gray-500">
                  <p>Formats acceptés: JPG, PNG, WebP</p>
                  <p>Taille maximum: {maxSize}MB</p>
                  <p>Recommandé: 400x400px</p>
                </div>
              </div>
            )}
          </div>

          {/* Error Message */}
          {error && (
            <div className="mt-2 flex items-center text-red-600 text-sm">
              <AlertCircle className="h-4 w-4 mr-1" />
              {error}
            </div>
          )}
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileSelect}
        className="hidden"
      />
    </div>
  );
};

export default ImageUpload;
