import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Trophy
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';
import { useAuth } from '../../contexts/AuthContext';

interface Player {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  jerseyNumber: number;
  position: string;
  height?: number;
  weight?: number;
  birthDate?: string;
  nationality?: string;
  profileImage?: string;
  isActive: boolean;
  joinDate: string;
}

const PlayersManagement: React.FC = () => {
  const { token } = useAuth();
  const [players, setPlayers] = useState<Player[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);

  // Mock data - Replace with API call
  useEffect(() => {
    const mockPlayers: Player[] = [
      {
        id: '1',
        user: {
          firstName: 'Hamza',
          lastName: 'Bedoui',
          email: '<EMAIL>',
          phone: '+216 98 123 456'
        },
        jerseyNumber: 10,
        position: 'Attaquant',
        height: 180,
        weight: 75,
        birthDate: '1995-03-15',
        nationality: 'Tunisienne',
        profileImage: 'http://localhost:5001/images/players/joueur_5.jpg',
        isActive: true,
        joinDate: '2023-01-15'
      },
      {
        id: '2',
        user: {
          firstName: 'Ahmed',
          lastName: 'Ben Ali',
          email: '<EMAIL>',
          phone: '+216 97 654 321'
        },
        jerseyNumber: 1,
        position: 'Gardien',
        height: 185,
        weight: 80,
        birthDate: '1992-07-22',
        nationality: 'Tunisienne',
        profileImage: 'http://localhost:5001/images/players/joueur_1.jpg',
        isActive: true,
        joinDate: '2022-08-01'
      },
      {
        id: '3',
        user: {
          firstName: 'Mohamed',
          lastName: 'Trabelsi',
          email: '<EMAIL>',
          phone: '+216 99 888 777'
        },
        jerseyNumber: 4,
        position: 'Défenseur',
        height: 178,
        weight: 73,
        birthDate: '1994-11-08',
        nationality: 'Tunisienne',
        profileImage: 'http://localhost:5001/images/players/joueur_2.jpg',
        isActive: true,
        joinDate: '2023-03-10'
      }
    ];

    setTimeout(() => {
      setPlayers(mockPlayers);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredPlayers = players.filter(player =>
    player.user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    player.user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    player.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
    player.jerseyNumber.toString().includes(searchTerm)
  );

  const getPositionColor = (position: string) => {
    switch (position.toLowerCase()) {
      case 'gardien':
        return 'bg-yellow-100 text-yellow-800';
      case 'défenseur':
        return 'bg-blue-100 text-blue-800';
      case 'milieu':
        return 'bg-green-100 text-green-800';
      case 'attaquant':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const calculateAge = (birthDate: string) => {
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des joueurs</h1>
            <p className="mt-1 text-sm text-gray-600">
              Gérez l'effectif d'Espoir Sportif de Chorbane
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              onClick={() => window.location.href = '/dashboard/players/add'}
              className="btn btn-primary"
            >
              <Plus className="h-5 w-5 mr-2" />
              Ajouter un joueur
            </button>
          </div>
        </div>

        {/* Search and filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher un joueur..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select className="input">
                <option value="">Toutes les positions</option>
                <option value="gardien">Gardien</option>
                <option value="défenseur">Défenseur</option>
                <option value="milieu">Milieu</option>
                <option value="attaquant">Attaquant</option>
              </select>
              <select className="input">
                <option value="">Tous les statuts</option>
                <option value="active">Actif</option>
                <option value="inactive">Inactif</option>
              </select>
            </div>
          </div>
        </div>

        {/* Players grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPlayers.map((player) => (
            <div key={player.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              {/* Player image */}
              <div className="relative h-48 bg-gray-200">
                {player.profileImage ? (
                  <img
                    src={player.profileImage}
                    alt={`${player.user.firstName} ${player.user.lastName}`}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <User className="h-16 w-16 text-gray-400" />
                  </div>
                )}
                <div className="absolute top-4 left-4">
                  <span className="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-bold">
                    #{player.jerseyNumber}
                  </span>
                </div>
                <div className="absolute top-4 right-4">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPositionColor(player.position)}`}>
                    {player.position}
                  </span>
                </div>
              </div>

              {/* Player info */}
              <div className="p-6">
                <div className="mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {player.user.firstName} {player.user.lastName}
                  </h3>
                  <div className="flex items-center text-sm text-gray-600 mt-1">
                    <MapPin className="h-4 w-4 mr-1" />
                    {player.nationality}
                    {player.birthDate && (
                      <>
                        <span className="mx-2">•</span>
                        <Calendar className="h-4 w-4 mr-1" />
                        {calculateAge(player.birthDate)} ans
                      </>
                    )}
                  </div>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center text-gray-600">
                    <Mail className="h-4 w-4 mr-2" />
                    {player.user.email}
                  </div>
                  {player.user.phone && (
                    <div className="flex items-center text-gray-600">
                      <Phone className="h-4 w-4 mr-2" />
                      {player.user.phone}
                    </div>
                  )}
                  {(player.height || player.weight) && (
                    <div className="flex items-center text-gray-600">
                      <Trophy className="h-4 w-4 mr-2" />
                      {player.height && `${player.height}cm`}
                      {player.height && player.weight && ' • '}
                      {player.weight && `${player.weight}kg`}
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    player.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {player.isActive ? 'Actif' : 'Inactif'}
                  </span>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedPlayer(player)}
                      className="p-2 text-gray-400 hover:text-green-600 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-red-600 transition-colors">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredPlayers.length === 0 && (
          <div className="text-center py-12">
            <User className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun joueur trouvé</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm ? 'Essayez de modifier vos critères de recherche.' : 'Commencez par ajouter un joueur.'}
            </p>
          </div>
        )}

        {/* Stats summary */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Résumé de l'effectif</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{players.length}</p>
              <p className="text-sm text-gray-600">Total joueurs</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">
                {players.filter(p => p.isActive).length}
              </p>
              <p className="text-sm text-gray-600">Actifs</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">
                {players.filter(p => p.position === 'Gardien').length}
              </p>
              <p className="text-sm text-gray-600">Gardiens</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">
                {players.filter(p => p.position === 'Attaquant').length}
              </p>
              <p className="text-sm text-gray-600">Attaquants</p>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default PlayersManagement;
