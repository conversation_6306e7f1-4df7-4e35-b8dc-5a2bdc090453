# Dockerfile pour le développement avec hot reload
FROM node:18-alpine

# Installer les dépendances système nécessaires
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Créer un utilisateur non-root pour la sécurité
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Définir le répertoire de travail
WORKDIR /app

# Changer le propriétaire du répertoire
RUN chown -R nodejs:nodejs /app

# Installer nodemon globalement pour le hot reload
RUN npm install -g nodemon

# Copier les fichiers de dépendances
COPY package*.json ./
COPY prisma ./prisma/

# Changer le propriétaire des fichiers copiés
RUN chown -R nodejs:nodejs /app

# Changer vers l'utilisateur nodejs pour l'installation
USER nodejs

# Installer toutes les dépendances (dev + prod)
RUN npm install

# Revenir à root pour ajuster les permissions finales
USER root
RUN chown -R nodejs:nodejs /app

# Changer vers l'utilisateur non-root
USER nodejs

# Générer le client Prisma
RUN npx prisma generate

# Revenir à root pour créer les dossiers
USER root

# Créer les dossiers nécessaires
RUN mkdir -p uploads logs && \
    chown -R nodejs:nodejs /app

# Changer vers l'utilisateur non-root
USER nodejs

# Exposer le port
EXPOSE 5000

# Définir les variables d'environnement par défaut
ENV NODE_ENV=development
ENV PORT=5000

# Vérification de santé
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
    CMD curl -f http://localhost:5000/api/health || exit 1

# Utiliser dumb-init pour gérer les signaux
ENTRYPOINT ["dumb-init", "--"]

# Commande par défaut pour le développement
CMD ["npm", "run", "dev"]
