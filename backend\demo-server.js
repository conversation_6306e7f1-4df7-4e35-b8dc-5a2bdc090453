const express = require('express');
const cors = require('cors');
const path = require('path');
const { faker } = require('@faker-js/faker');
const bcrypt = require('bcryptjs');

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use('/images', express.static(path.join(__dirname, 'public/images')));

// Données factices en mémoire
let users = [];
let players = [];
let teams = [];
let matches = [];
let news = [];

// Positions et pieds préférés
const POSITIONS = ['GOALKEEPER', 'DEFENDER', 'MIDFIELDER', 'FORWARD'];
const PREFERRED_FOOT = ['LEFT', 'RIGHT', 'BOTH'];
const TUNISIAN_TEAMS = [
  'Espérance Sportive de Tunis',
  'Club Africain',
  'Étoile Sportive du Sahel',
  'Club Sportif Sfaxien',
  'Union Sportive Monastirienne',
  'Club Athlétique Bizertin',
  'Olympique de Béja',
  'Avenir Sportif de La Marsa',
  'Stade Tunisien',
  'Union Sportive Ben Guerdane'
];

// Fonction pour générer des données factices
async function generateFakeData() {
  console.log('🎲 Génération des données factices...');

  // Générer 3 admins
  for (let i = 0; i < 3; i++) {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    users.push({
      id: `admin_${i + 1}`,
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: `admin${i + 1}@esc.tn`,
      password: hashedPassword,
      role: 'ADMIN',
      phone: faker.phone.number('+216 ## ### ###'),
      avatar: `https://images.unsplash.com/photo-${faker.number.int({ min: 1500000000000, max: 1600000000000 })}?w=400&h=400&fit=crop&crop=face`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }

  // Générer 30 joueurs
  for (let i = 0; i < 30; i++) {
    const hashedPassword = await bcrypt.hash('player123', 10);
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    
    const user = {
      id: `player_${i + 1}`,
      firstName,
      lastName,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@esc.tn`,
      password: hashedPassword,
      role: 'PLAYER',
      phone: faker.phone.number('+216 ## ### ###'),
      avatar: `https://images.unsplash.com/photo-${faker.number.int({ min: 1500000000000, max: 1600000000000 })}?w=400&h=400&fit=crop&crop=face`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    users.push(user);

    const player = {
      id: `player_${i + 1}`,
      userId: user.id,
      user: user,
      dateOfBirth: faker.date.between({ from: new Date('1990-01-01'), to: new Date('2005-12-31') }).toISOString(),
      nationality: faker.helpers.arrayElement(['Tunisie', 'Algérie', 'Maroc', 'France', 'Sénégal']),
      position: faker.helpers.arrayElement(POSITIONS),
      jerseyNumber: i + 1,
      height: faker.number.int({ min: 165, max: 195 }),
      weight: faker.number.int({ min: 60, max: 90 }),
      preferredFoot: faker.helpers.arrayElement(PREFERRED_FOOT),
      contractStart: faker.date.between({ from: new Date('2023-01-01'), to: new Date('2024-06-30') }).toISOString(),
      contractEnd: faker.date.between({ from: new Date('2025-01-01'), to: new Date('2027-12-31') }).toISOString(),
      salary: faker.number.int({ min: 2000, max: 15000 }),
      biography: faker.lorem.paragraph(3),
      previousClubs: faker.helpers.arrayElements(TUNISIAN_TEAMS, 2).join(', '),
      achievements: faker.lorem.sentence(),
      profileImage: `https://images.unsplash.com/photo-${faker.number.int({ min: 1500000000000, max: 1600000000000 })}?w=400&h=400&fit=crop&crop=face`,
      isActive: faker.datatype.boolean(0.9),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    players.push(player);
  }

  // Générer 10 équipes
  for (let i = 0; i < 10; i++) {
    teams.push({
      id: `team_${i + 1}`,
      name: TUNISIAN_TEAMS[i],
      logo: `https://images.unsplash.com/photo-${faker.number.int({ min: 1500000000000, max: 1600000000000 })}?w=300&h=300&fit=crop`,
      city: faker.location.city(),
      stadium: `Stade ${faker.location.streetName()}`,
      foundedYear: faker.number.int({ min: 1920, max: 2000 }),
      description: faker.lorem.paragraph(2),
      website: `https://www.${TUNISIAN_TEAMS[i].toLowerCase().replace(/\s+/g, '')}.tn`,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }

  // Générer 15 matchs
  for (let i = 0; i < 15; i++) {
    const homeTeam = faker.helpers.arrayElement(teams);
    let awayTeam = faker.helpers.arrayElement(teams);
    while (awayTeam.id === homeTeam.id) {
      awayTeam = faker.helpers.arrayElement(teams);
    }

    const matchDate = faker.date.between({ from: new Date('2024-09-01'), to: new Date('2025-05-31') });
    const isPlayed = matchDate < new Date();

    matches.push({
      id: `match_${i + 1}`,
      homeTeamId: homeTeam.id,
      awayTeamId: awayTeam.id,
      homeTeam: homeTeam,
      awayTeam: awayTeam,
      matchDate: matchDate.toISOString(),
      venue: homeTeam.stadium,
      homeScore: isPlayed ? faker.number.int({ min: 0, max: 5 }) : null,
      awayScore: isPlayed ? faker.number.int({ min: 0, max: 5 }) : null,
      status: isPlayed ? 'FINISHED' : faker.helpers.arrayElement(['SCHEDULED', 'LIVE']),
      season: '2024-2025',
      competition: faker.helpers.arrayElement(['Championnat', 'Coupe de Tunisie', 'Ligue des Champions']),
      description: faker.lorem.sentence(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }

  // Générer 25 articles
  for (let i = 0; i < 25; i++) {
    news.push({
      id: `news_${i + 1}`,
      title: faker.lorem.sentence({ min: 5, max: 10 }),
      content: faker.lorem.paragraphs(5, '\n\n'),
      excerpt: faker.lorem.paragraph(),
      featuredImage: `https://images.unsplash.com/photo-${faker.number.int({ min: 1500000000000, max: 1600000000000 })}?w=800&h=400&fit=crop`,
      category: faker.helpers.arrayElement(['Match', 'Transfert', 'Formation', 'Club', 'Communauté']),
      tags: faker.helpers.arrayElements(['football', 'esc', 'tunisie', 'sport', 'équipe', 'victoire'], 3).join(','),
      isPublished: faker.datatype.boolean(0.8),
      publishedAt: faker.date.between({ from: new Date('2024-01-01'), to: new Date() }).toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }

  console.log('✅ Données factices générées:');
  console.log(`   - ${users.filter(u => u.role === 'ADMIN').length} administrateurs`);
  console.log(`   - ${players.length} joueurs`);
  console.log(`   - ${teams.length} équipes`);
  console.log(`   - ${matches.length} matchs`);
  console.log(`   - ${news.length} articles`);
}

// Routes API
app.get('/api/health', (req, res) => {
  res.json({
    message: 'API Espoir Sportif de Chorbane - Environnement de démonstration',
    timestamp: new Date().toISOString(),
    environment: 'demo',
    data: {
      users: users.length,
      players: players.length,
      teams: teams.length,
      matches: matches.length,
      news: news.length
    }
  });
});

// Routes des joueurs
app.get('/api/players', (req, res) => {
  res.json({
    message: 'Liste des joueurs',
    players: players
  });
});

app.get('/api/players/:id', (req, res) => {
  const player = players.find(p => p.id === req.params.id);
  if (!player) {
    return res.status(404).json({ message: 'Joueur non trouvé' });
  }
  res.json({
    message: 'Détails du joueur',
    player: player
  });
});

// Routes des équipes
app.get('/api/teams', (req, res) => {
  res.json({
    message: 'Liste des équipes',
    teams: teams
  });
});

// Routes des matchs
app.get('/api/matches', (req, res) => {
  res.json({
    message: 'Liste des matchs',
    matches: matches
  });
});

// Routes des actualités
app.get('/api/news', (req, res) => {
  const publishedNews = news.filter(n => n.isPublished);
  res.json({
    message: 'Liste des actualités',
    news: publishedNews
  });
});

// Route de connexion simplifiée
app.post('/api/auth/login', async (req, res) => {
  const { email, password } = req.body;
  
  const user = users.find(u => u.email === email);
  if (!user) {
    return res.status(401).json({ message: 'Email ou mot de passe incorrect' });
  }

  const isValidPassword = await bcrypt.compare(password, user.password);
  if (!isValidPassword) {
    return res.status(401).json({ message: 'Email ou mot de passe incorrect' });
  }

  // Token factice pour la démo
  const token = 'demo_token_' + user.id;
  
  res.json({
    message: 'Connexion réussie',
    token: token,
    user: {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      avatar: user.avatar
    }
  });
});

// Middleware de gestion d'erreurs
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Erreur interne du serveur' });
});

// Route 404
app.use((req, res) => {
  res.status(404).json({ message: 'Route non trouvée' });
});

// Démarrage du serveur
async function startServer() {
  try {
    await generateFakeData();
    
    app.listen(PORT, () => {
      console.log(`🚀 Serveur de démonstration démarré sur le port ${PORT}`);
      console.log(`🌍 Environnement: DEMO`);
      console.log(`📊 API disponible sur: http://localhost:${PORT}/api`);
      console.log(`🏥 Health check: http://localhost:${PORT}/api/health`);
      console.log(`\n🔑 Comptes de test:`);
      console.log(`   Admin: <EMAIL> / admin123`);
      console.log(`   Admin: <EMAIL> / admin123`);
      console.log(`   Admin: <EMAIL> / admin123`);
      console.log(`\n📱 Frontend: http://localhost:3000`);
    });
  } catch (error) {
    console.error('❌ Erreur lors du démarrage:', error);
    process.exit(1);
  }
}

startServer();
