const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// GET /api/matches - Récupérer tous les matchs (public)
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      upcoming = false,
      past = false
    } = req.query;
    const skip = (page - 1) * limit;

    // Construire les filtres
    const where = {};
    if (status) {
      where.status = status;
    }
    if (upcoming === 'true') {
      where.matchDate = {
        gte: new Date()
      };
    }
    if (past === 'true') {
      where.matchDate = {
        lt: new Date()
      };
    }

    const [matches, total] = await Promise.all([
      prisma.match.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          matchPlayers: {
            include: {
              player: {
                include: {
                  user: {
                    select: {
                      firstName: true,
                      lastName: true
                    }
                  }
                }
              }
            }
          },
          matchEvents: {
            orderBy: {
              minute: 'asc'
            }
          }
        },
        orderBy: {
          matchDate: 'desc'
        }
      }),
      prisma.match.count({ where })
    ]);

    res.json({
      message: 'Matchs récupérés avec succès',
      matches,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des matchs:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/matches/:id - Récupérer un match par ID (public)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const match = await prisma.match.findUnique({
      where: { id },
      include: {
        matchPlayers: {
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true,
                    avatar: true
                  }
                }
              }
            }
          },
          orderBy: [
            { isStarter: 'desc' },
            { player: { jerseyNumber: 'asc' } }
          ]
        },
        matchEvents: {
          orderBy: {
            minute: 'asc'
          }
        }
      }
    });

    if (!match) {
      return res.status(404).json({
        message: 'Match non trouvé'
      });
    }

    res.json({
      message: 'Match récupéré avec succès',
      match
    });

  } catch (error) {
    console.error('Erreur lors de la récupération du match:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/matches - Créer un nouveau match (Admin seulement)
router.post('/', [
  body('awayTeam')
    .notEmpty()
    .withMessage('Équipe adverse requise'),
  body('matchDate')
    .isISO8601()
    .withMessage('Date de match invalide'),
  body('location')
    .notEmpty()
    .withMessage('Lieu du match requis'),
  body('isHome')
    .optional()
    .isBoolean()
    .withMessage('Indicateur domicile/extérieur invalide')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const {
      awayTeam,
      matchDate,
      location,
      description,
      isHome = true,
      homeTeam = 'Espoir Sportive Chorbane'
    } = req.body;

    const match = await prisma.match.create({
      data: {
        homeTeam: isHome ? homeTeam : awayTeam,
        awayTeam: isHome ? awayTeam : homeTeam,
        matchDate: new Date(matchDate),
        location,
        description,
        isHome
      }
    });

    res.status(201).json({
      message: 'Match créé avec succès',
      match
    });

  } catch (error) {
    console.error('Erreur lors de la création du match:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// PUT /api/matches/:id - Mettre à jour un match (Admin seulement)
router.put('/:id', [
  body('awayTeam')
    .optional()
    .notEmpty()
    .withMessage('Équipe adverse requise'),
  body('matchDate')
    .optional()
    .isISO8601()
    .withMessage('Date de match invalide'),
  body('location')
    .optional()
    .notEmpty()
    .withMessage('Lieu du match requis'),
  body('homeScore')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Score domicile invalide'),
  body('awayScore')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Score extérieur invalide'),
  body('status')
    .optional()
    .isIn(['SCHEDULED', 'LIVE', 'FINISHED', 'CANCELLED', 'POSTPONED'])
    .withMessage('Statut invalide')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const {
      awayTeam,
      matchDate,
      location,
      description,
      homeScore,
      awayScore,
      status
    } = req.body;

    // Vérifier que le match existe
    const existingMatch = await prisma.match.findUnique({
      where: { id }
    });

    if (!existingMatch) {
      return res.status(404).json({
        message: 'Match non trouvé'
      });
    }

    const updatedMatch = await prisma.match.update({
      where: { id },
      data: {
        awayTeam,
        matchDate: matchDate ? new Date(matchDate) : undefined,
        location,
        description,
        homeScore,
        awayScore,
        status
      },
      include: {
        matchPlayers: {
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        },
        matchEvents: true
      }
    });

    res.json({
      message: 'Match mis à jour avec succès',
      match: updatedMatch
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour du match:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/matches/:id/players - Ajouter des joueurs à un match (Admin seulement)
router.post('/:id/players', [
  body('players')
    .isArray()
    .withMessage('Liste de joueurs requise')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { players } = req.body;

    // Vérifier que le match existe
    const match = await prisma.match.findUnique({
      where: { id }
    });

    if (!match) {
      return res.status(404).json({
        message: 'Match non trouvé'
      });
    }

    // Supprimer les anciennes sélections
    await prisma.matchPlayer.deleteMany({
      where: { matchId: id }
    });

    // Ajouter les nouveaux joueurs
    const matchPlayers = await Promise.all(
      players.map(({ playerId, isStarter = false }) =>
        prisma.matchPlayer.create({
          data: {
            matchId: id,
            playerId,
            isStarter
          },
          include: {
            player: {
              include: {
                user: {
                  select: {
                    firstName: true,
                    lastName: true
                  }
                }
              }
            }
          }
        })
      )
    );

    res.json({
      message: 'Joueurs ajoutés au match avec succès',
      matchPlayers
    });

  } catch (error) {
    console.error('Erreur lors de l\'ajout des joueurs au match:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// DELETE /api/matches/:id - Supprimer un match (Admin seulement)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que le match existe
    const existingMatch = await prisma.match.findUnique({
      where: { id }
    });

    if (!existingMatch) {
      return res.status(404).json({
        message: 'Match non trouvé'
      });
    }

    await prisma.match.delete({
      where: { id }
    });

    res.json({
      message: 'Match supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression du match:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
