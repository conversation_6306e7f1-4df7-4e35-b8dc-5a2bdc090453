const { PrismaClient } = require('@prisma/client');
const { faker } = require('@faker-js/faker');
const { hashPassword } = require('./auth');

const prisma = new PrismaClient();

// Configuration Faker en français
faker.locale = 'fr';

// Listes de données réalistes pour le football tunisien
const tunisianFirstNames = [
  '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>che<PERSON>', '<PERSON><PERSON>'
];

const tunisianLastNames = [
  '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>i', '<PERSON>da<PERSON>i', '<PERSON><PERSON>i'
];

const tuni<PERSON><PERSON><PERSON> = [
  '<PERSON><PERSON>', '<PERSON>fax', '<PERSON><PERSON>e', '<PERSON>rouan', '<PERSON><PERSON>te', '<PERSON><PERSON><PERSON>', '<PERSON>na',
  '<PERSON><PERSON><PERSON>', '<PERSON>st<PERSON>', '<PERSON> <PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>den<PERSON>', '<PERSON>be<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>a', 'Siliana', 'Manouba', 'Zaghouan'
];

const footballClubs = [
  'AS Chorbane', 'ES Tunis', 'CA Bizertin', 'US Monastir', 'CS Sfaxien',
  'Club Africain', 'Stade Tunisien', 'AS Soliman', 'US Ben Guerdane',
  'ES Métlaoui', 'CS Hammam-Lif', 'AS Marsa', 'US Tataouine', 'ES Zarzis'
];

const stadiums = [
  'Stade Municipal Chorbane', 'Stade Olympique de Radès', 'Stade Taïeb Mhiri',
  'Stade Mustapha Ben Jannet', 'Stade Hédi Enneifer', 'Stade 15 Octobre',
  'Stade Chedli Zouiten', 'Stade Zouhaier Essaafi', 'Stade Municipal de Bizerte'
];

// Fonctions utilitaires
const getRandomElement = (array) => array[Math.floor(Math.random() * array.length)];

const generateTunisianName = () => ({
  firstName: getRandomElement(tunisianFirstNames),
  lastName: getRandomElement(tunisianLastNames)
});

const generateRealisticEmail = (firstName, lastName) => {
  const domains = ['gmail.com', 'yahoo.fr', 'hotmail.com', 'outlook.com', 'live.com'];
  const cleanFirstName = firstName.toLowerCase().replace(/\s+/g, '');
  const cleanLastName = lastName.toLowerCase().replace(/\s+/g, '');
  const domain = getRandomElement(domains);

  const emailFormats = [
    `${cleanFirstName}.${cleanLastName}@${domain}`,
    `${cleanFirstName}${cleanLastName}@${domain}`,
    `${cleanFirstName}_${cleanLastName}@${domain}`,
    `${cleanFirstName}${Math.floor(Math.random() * 100)}@${domain}`
  ];

  return getRandomElement(emailFormats);
};

const generateTunisianPhone = () => {
  const prefixes = ['20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59'];
  const prefix = getRandomElement(prefixes);
  const number = faker.string.numeric(6);
  return `+216 ${prefix} ${number.substring(0, 3)} ${number.substring(3)}`;
};

async function main() {
  console.log('🌱 Début du seeding de la base de données...');

  try {
    // Nettoyer la base de données
    await prisma.galleryMedia.deleteMany();
    await prisma.gallery.deleteMany();
    await prisma.articleTag.deleteMany();
    await prisma.tag.deleteMany();
    await prisma.comment.deleteMany();
    await prisma.article.deleteMany();
    await prisma.trainingAttendance.deleteMany();
    await prisma.training.deleteMany();
    await prisma.matchEvent.deleteMany();
    await prisma.matchPlayer.deleteMany();
    await prisma.match.deleteMany();
    await prisma.playerStats.deleteMany();
    await prisma.player.deleteMany();
    await prisma.user.deleteMany();

    console.log('🧹 Base de données nettoyée');

    // Créer l'administrateur
    const adminPassword = await hashPassword('admin123');
    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: adminPassword,
        firstName: 'Admin',
        lastName: 'Espoir Sportive',
        role: 'ADMIN',
        phone: '+216 20 123 456'
      }
    });

    console.log('👤 Administrateur créé');

    // Générer des joueurs avec Faker
    const positionCounts = { GOALKEEPER: 2, DEFENDER: 6, MIDFIELDER: 8, FORWARD: 6 }; // Formation réaliste

    const playerPassword = await hashPassword('player123');
    const players = [];
    let jerseyNumber = 1;

    // Créer les joueurs par position
    for (const [position, count] of Object.entries(positionCounts)) {
      for (let i = 0; i < count; i++) {
        const { firstName, lastName } = generateTunisianName();
        const email = generateRealisticEmail(firstName, lastName);

        // Créer l'utilisateur
        const user = await prisma.user.create({
          data: {
            email,
            password: playerPassword,
            firstName,
            lastName,
            role: 'PLAYER',
            phone: generateTunisianPhone(),
            avatar: faker.image.avatar()
          }
        });

        // Générer des caractéristiques physiques réalistes selon la position
        let height, weight;
        switch (position) {
          case 'GOALKEEPER':
            height = faker.number.float({ min: 1.80, max: 1.95, precision: 0.01 });
            weight = faker.number.float({ min: 75, max: 90, precision: 0.1 });
            break;
          case 'DEFENDER':
            height = faker.number.float({ min: 1.75, max: 1.90, precision: 0.01 });
            weight = faker.number.float({ min: 70, max: 85, precision: 0.1 });
            break;
          case 'MIDFIELDER':
            height = faker.number.float({ min: 1.65, max: 1.80, precision: 0.01 });
            weight = faker.number.float({ min: 65, max: 80, precision: 0.1 });
            break;
          case 'FORWARD':
            height = faker.number.float({ min: 1.70, max: 1.85, precision: 0.01 });
            weight = faker.number.float({ min: 68, max: 82, precision: 0.1 });
            break;
        }

        // Créer le joueur
        const player = await prisma.player.create({
          data: {
            userId: user.id,
            jerseyNumber,
            position,
            height,
            weight,
            birthDate: faker.date.between({
              from: new Date('1995-01-01'),
              to: new Date('2005-12-31')
            }),
            nationality: 'Tunisie',
            joinDate: faker.date.between({
              from: new Date('2020-01-01'),
              to: new Date('2024-01-01')
            }),
            biography: faker.lorem.paragraph({ min: 2, max: 4 })
          }
        });

        // Créer des statistiques réalistes selon la position
        let goals, assists, matchesPlayed;
        matchesPlayed = faker.number.int({ min: 5, max: 20 });

        switch (position) {
          case 'GOALKEEPER':
            goals = faker.number.int({ min: 0, max: 1 });
            assists = faker.number.int({ min: 0, max: 2 });
            break;
          case 'DEFENDER':
            goals = faker.number.int({ min: 0, max: 4 });
            assists = faker.number.int({ min: 0, max: 6 });
            break;
          case 'MIDFIELDER':
            goals = faker.number.int({ min: 1, max: 8 });
            assists = faker.number.int({ min: 2, max: 12 });
            break;
          case 'FORWARD':
            goals = faker.number.int({ min: 3, max: 15 });
            assists = faker.number.int({ min: 1, max: 8 });
            break;
        }

        await prisma.playerStats.create({
          data: {
            playerId: player.id,
            season: '2024-2025',
            matchesPlayed,
            goals,
            assists,
            yellowCards: faker.number.int({ min: 0, max: 5 }),
            redCards: faker.number.int({ min: 0, max: 2 }),
            minutesPlayed: matchesPlayed * faker.number.int({ min: 45, max: 90 })
          }
        });

        players.push(player);
        jerseyNumber++;
      }
    }

    console.log('⚽ Joueurs créés');

    // Créer des supporters avec Faker
    const supporterPassword = await hashPassword('supporter123');
    const supportersCount = 15;

    for (let i = 0; i < supportersCount; i++) {
      const { firstName, lastName } = generateTunisianName();
      const email = generateRealisticEmail(firstName, lastName);

      await prisma.user.create({
        data: {
          email,
          password: supporterPassword,
          firstName,
          lastName,
          role: 'SUPPORTER',
          phone: generateTunisianPhone(),
          avatar: faker.image.avatar()
        }
      });
    }

    // Créer le supporter principal pour les tests
    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: supporterPassword,
        firstName: 'Supporter',
        lastName: 'Fidèle',
        role: 'SUPPORTER',
        phone: generateTunisianPhone(),
        avatar: faker.image.avatar()
      }
    });

    console.log('🎉 Supporters créés');

    // Créer des matchs avec Faker
    const matches = [];
    const matchCount = 15;

    for (let i = 0; i < matchCount; i++) {
      // Générer une date de match réaliste (entre -3 mois et +3 mois)
      const matchDate = faker.date.between({
        from: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // -3 mois
        to: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)    // +3 mois
      });

      const isFinished = matchDate < new Date();
      const isHome = faker.datatype.boolean();
      const awayTeam = getRandomElement(footballClubs.filter(team => team !== 'Espoir Sportive Chorbane'));
      const location = isHome ? 'Stade Municipal Chorbane' : getRandomElement(stadiums);

      let homeScore = null;
      let awayScore = null;
      let status = 'SCHEDULED';

      if (isFinished) {
        homeScore = faker.number.int({ min: 0, max: 5 });
        awayScore = faker.number.int({ min: 0, max: 5 });
        status = 'FINISHED';
      } else if (faker.number.float() < 0.1) { // 10% de chance d'être en cours
        status = 'LIVE';
        homeScore = faker.number.int({ min: 0, max: 3 });
        awayScore = faker.number.int({ min: 0, max: 3 });
      }

      const match = await prisma.match.create({
        data: {
          homeTeam: isHome ? 'Espoir Sportive Chorbane' : awayTeam,
          awayTeam: isHome ? awayTeam : 'Espoir Sportive Chorbane',
          matchDate,
          location,
          homeScore,
          awayScore,
          status,
          description: faker.lorem.sentence({ min: 5, max: 10 }),
          isHome
        }
      });
      matches.push(match);
    }

    console.log('🏆 Matchs créés');

    // Créer des entraînements avec Faker
    const trainingTypes = [
      'Entraînement technique',
      'Préparation physique',
      'Tactique défensive',
      'Tactique offensive',
      'Séance de tirs',
      'Travail de passes',
      'Condition physique',
      'Match d\'entraînement'
    ];

    const trainingLocations = [
      'Terrain d\'entraînement Chorbane',
      'Stade Municipal Chorbane',
      'Terrain annexe',
      'Salle de musculation',
      'Terrain synthétique'
    ];

    for (let i = 0; i < 20; i++) {
      // Générer une date d'entraînement (entre -2 mois et +1 mois)
      const trainingDate = faker.date.between({
        from: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), // -2 mois
        to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)    // +1 mois
      });

      const trainingType = getRandomElement(trainingTypes);
      const location = getRandomElement(trainingLocations);

      const training = await prisma.training.create({
        data: {
          title: trainingType,
          description: faker.lorem.paragraph({ min: 1, max: 3 }),
          date: trainingDate,
          location,
          duration: faker.number.int({ min: 60, max: 120 }) // 60-120 minutes
        }
      });

      // Ajouter les présences pour tous les joueurs
      for (const player of players) {
        const isPresent = faker.datatype.boolean({ probability: 0.85 }); // 85% de présence
        const notes = isPresent && faker.datatype.boolean({ probability: 0.3 })
          ? faker.helpers.arrayElement([
              'Excellente performance',
              'Bonne séance',
              'Très motivé',
              'Progression notable',
              'Besoin de repos',
              'Léger retard',
              'Performance moyenne'
            ])
          : null;

        await prisma.trainingAttendance.create({
          data: {
            trainingId: training.id,
            playerId: player.id,
            isPresent,
            notes
          }
        });
      }
    }

    console.log('🏃 Entraînements créés');

    // Créer des articles avec Faker
    const articleTypes = [
      'Résultat de match',
      'Nouveau recrutement',
      'Préparation saison',
      'Interview joueur',
      'Actualité club',
      'Événement spécial',
      'Communiqué officiel'
    ];

    const articleTitles = {
      'Résultat de match': [
        'Victoire éclatante contre {team}',
        'Match nul héroïque face à {team}',
        'Défaite courageuse contre {team}',
        'Remontada spectaculaire contre {team}',
        'Derby passionnant contre {team}'
      ],
      'Nouveau recrutement': [
        'Bienvenue à {player} !',
        'Nouveau renfort : {player} rejoint l\'équipe',
        'Recrutement : {player} signe à Espoir Sportive',
        'Transfert : {player} nous rejoint'
      ],
      'Préparation saison': [
        'Préparation intensive pour la nouvelle saison',
        'Stage de préparation réussi',
        'L\'équipe se prépare pour les défis à venir',
        'Reprise des entraînements'
      ],
      'Interview joueur': [
        'Interview exclusive avec {player}',
        '{player} se confie sur ses ambitions',
        'Rencontre avec {player}, pilier de l\'équipe'
      ],
      'Actualité club': [
        'Assemblée générale du club',
        'Nouveaux équipements pour l\'équipe',
        'Partenariat avec {sponsor}',
        'Travaux d\'amélioration du stade'
      ]
    };

    for (let i = 0; i < 12; i++) {
      const articleType = getRandomElement(articleTypes);
      const titleTemplates = articleTitles[articleType] || ['Article sur {topic}'];
      let title = getRandomElement(titleTemplates);

      // Remplacer les placeholders
      if (title.includes('{team}')) {
        title = title.replace('{team}', getRandomElement(footballClubs));
      }
      if (title.includes('{player}')) {
        const randomPlayer = getRandomElement(players);
        const playerUser = await prisma.user.findUnique({ where: { id: randomPlayer.userId } });
        title = title.replace('{player}', `${playerUser.firstName} ${playerUser.lastName}`);
      }
      if (title.includes('{sponsor}')) {
        title = title.replace('{sponsor}', faker.company.name());
      }
      if (title.includes('{topic}')) {
        title = title.replace('{topic}', faker.lorem.words(2));
      }

      const content = faker.lorem.paragraphs({ min: 3, max: 8 }, '\n\n');
      const excerpt = faker.lorem.paragraph({ min: 1, max: 2 });
      const isPublished = faker.datatype.boolean({ probability: 0.8 }); // 80% publiés

      await prisma.article.create({
        data: {
          title,
          content,
          excerpt,
          isPublished,
          authorId: admin.id,
          coverImage: faker.datatype.boolean({ probability: 0.6 }) ? faker.image.urlLoremFlickr({ category: 'sports' }) : null
        }
      });
    }

    console.log('📰 Articles créés');

    // Créer des tags avec Faker
    const tagNames = [
      'Match', 'Entraînement', 'Recrutement', 'Victoire', 'Défaite', 'Championnat',
      'Coupe', 'Derby', 'Préparation', 'Transfert', 'Blessure', 'Retour', 'Performance',
      'Tactique', 'Jeunes', 'Vétérans', 'Supporters', 'Stade', 'Équipement', 'Sponsor'
    ];

    const createdTags = [];
    for (const tagName of tagNames) {
      const tag = await prisma.tag.create({
        data: { name: tagName }
      });
      createdTags.push(tag);
    }

    console.log('🏷️ Tags créés');

    // Créer des galeries avec Faker
    const galleryThemes = [
      'Photos de la saison 2024-2025',
      'Entraînements de préparation',
      'Matchs à domicile',
      'Déplacements de l\'équipe',
      'Célébrations et victoires',
      'Coulisses du club',
      'Supporters et ambiance'
    ];

    for (let i = 0; i < 5; i++) {
      const gallery = await prisma.gallery.create({
        data: {
          title: getRandomElement(galleryThemes),
          description: faker.lorem.paragraph({ min: 1, max: 3 }),
          coverImage: faker.image.urlLoremFlickr({ category: 'sports' })
        }
      });

      // Ajouter des médias à chaque galerie
      const mediaCount = faker.number.int({ min: 5, max: 15 });
      for (let j = 0; j < mediaCount; j++) {
        const isVideo = faker.datatype.boolean({ probability: 0.2 }); // 20% de vidéos
        const fileType = isVideo ? 'video' : 'image';
        const fileName = `${fileType}_${faker.string.alphanumeric(8)}.${isVideo ? 'mp4' : 'jpg'}`;

        await prisma.galleryMedia.create({
          data: {
            galleryId: gallery.id,
            fileName,
            filePath: `/uploads/${fileName}`,
            fileType,
            caption: faker.lorem.sentence({ min: 3, max: 8 })
          }
        });
      }
    }

    console.log('📸 Galeries créées');

    // Associer des tags aux articles
    const articles = await prisma.article.findMany();
    for (const article of articles) {
      const numberOfTags = faker.number.int({ min: 1, max: 4 });
      const selectedTags = faker.helpers.arrayElements(createdTags, numberOfTags);

      for (const tag of selectedTags) {
        await prisma.articleTag.create({
          data: {
            articleId: article.id,
            tagId: tag.id
          }
        });
      }
    }

    // Créer des commentaires sur les articles publiés
    const publishedArticles = articles.filter(article => article.isPublished);
    const allUsers = await prisma.user.findMany();

    for (const article of publishedArticles) {
      const commentCount = faker.number.int({ min: 0, max: 8 });

      for (let i = 0; i < commentCount; i++) {
        const randomUser = getRandomElement(allUsers);

        await prisma.comment.create({
          data: {
            content: faker.lorem.paragraph({ min: 1, max: 3 }),
            articleId: article.id,
            authorId: randomUser.id
          }
        });
      }
    }

    // Créer des événements pour les matchs terminés
    const finishedMatches = matches.filter(match => match.status === 'FINISHED');

    for (const match of finishedMatches) {
      const eventCount = faker.number.int({ min: 2, max: 8 });

      for (let i = 0; i < eventCount; i++) {
        const eventTypes = ['GOAL', 'YELLOW_CARD', 'RED_CARD', 'SUBSTITUTION'];
        const eventType = getRandomElement(eventTypes);
        const minute = faker.number.int({ min: 1, max: 90 });
        const randomPlayer = getRandomElement(players);

        let cardType = null;
        if (eventType === 'YELLOW_CARD') cardType = 'YELLOW';
        if (eventType === 'RED_CARD') cardType = 'RED';

        await prisma.matchEvent.create({
          data: {
            matchId: match.id,
            playerId: randomPlayer.id,
            minute,
            type: eventType,
            cardType,
            description: faker.lorem.sentence({ min: 3, max: 6 })
          }
        });
      }
    }

    console.log(' Commentaires ajoutés');
    console.log('⚽ Événements de match créés');

    console.log('✅ Seeding terminé avec succès !');
    console.log('\n📋 Comptes créés :');
    console.log('👨‍💼 Admin: <EMAIL> / admin123');
    console.log('⚽ Joueur: <EMAIL> / player123');
    console.log('🎉 Supporter: <EMAIL> / supporter123');

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
