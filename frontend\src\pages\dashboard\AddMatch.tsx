import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Save,
  ArrowLeft,
  Calendar,
  MapPin,
  Trophy,
  DollarSign,
  Users,
  Clock
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface MatchFormData {
  homeTeam: string;
  awayTeam: string;
  matchDate: string;
  matchTime: string;
  location: string;
  competition: string;
  description: string;
  ticketPrice: number;
  availableTickets: number;
  isHomeMatch: boolean;
}

const AddMatch: React.FC = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState<MatchFormData>({
    homeTeam: 'Espoir Sportif de Chorbane',
    awayTeam: '',
    matchDate: '',
    matchTime: '15:00',
    location: 'Stade Municipal de Chorbane',
    competition: 'Championnat National',
    description: '',
    ticketPrice: 15,
    availableTickets: 300,
    isHomeMatch: true
  });

  const [saving, setSaving] = useState(false);

  const competitions = [
    'Championnat National',
    'Coupe de Tunisie',
    'Coupe de la CAF',
    'Match amical',
    'Championnat Amateur'
  ];

  const commonOpponents = [
    'AS Soliman',
    'CA Bizertin',
    'Club Africain',
    'Espérance Sportive de Tunis',
    'Étoile Sportive du Sahel',
    'CS Sfaxien',
    'US Monastir',
    'Stade Tunisien'
  ];

  const venues = [
    'Stade Municipal de Chorbane',
    'Stade Olympique de Radès',
    'Stade 15 Octobre (Bizerte)',
    'Stade Taïeb Mhiri (Sfax)',
    'Stade Mustapha Ben Jannet (Monastir)'
  ];

  const handleChange = (field: keyof MatchFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      
      // Auto-adjust teams based on home/away
      if (field === 'isHomeMatch') {
        if (value) {
          newData.homeTeam = 'Espoir Sportif de Chorbane';
          newData.location = 'Stade Municipal de Chorbane';
        } else {
          newData.awayTeam = 'Espoir Sportif de Chorbane';
        }
      }
      
      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Combine date and time
      const matchDateTime = `${formData.matchDate}T${formData.matchTime}:00`;
      
      const matchData = {
        ...formData,
        matchDate: matchDateTime
      };

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Match créé avec succès !');
      navigate('/dashboard/matches');
    } catch (error) {
      alert('Erreur lors de la création du match');
    } finally {
      setSaving(false);
    }
  };

  const formatDateTime = () => {
    if (!formData.matchDate || !formData.matchTime) return '';
    const date = new Date(`${formData.matchDate}T${formData.matchTime}`);
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={() => navigate('/dashboard/matches')}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
            >
              <ArrowLeft className="h-5 w-5" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Nouveau match</h1>
              <p className="mt-1 text-sm text-gray-600">
                Programmez un nouveau match pour Espoir Sportif de Chorbane
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Match Type */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Type de match</h3>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={formData.isHomeMatch}
                  onChange={() => handleChange('isHomeMatch', true)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">Match à domicile</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  checked={!formData.isHomeMatch}
                  onChange={() => handleChange('isHomeMatch', false)}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-gray-700">Match à l'extérieur</span>
              </label>
            </div>
          </div>

          {/* Teams */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Équipes</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Équipe à domicile *
                </label>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  {formData.isHomeMatch ? (
                    <input
                      type="text"
                      value={formData.homeTeam}
                      className="input pl-10 bg-gray-50"
                      readOnly
                    />
                  ) : (
                    <select
                      value={formData.homeTeam}
                      onChange={(e) => handleChange('homeTeam', e.target.value)}
                      className="input pl-10"
                      required
                    >
                      <option value="">Sélectionnez l'équipe à domicile</option>
                      {commonOpponents.map(team => (
                        <option key={team} value={team}>{team}</option>
                      ))}
                    </select>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Équipe visiteur *
                </label>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  {!formData.isHomeMatch ? (
                    <input
                      type="text"
                      value="Espoir Sportif de Chorbane"
                      className="input pl-10 bg-gray-50"
                      readOnly
                    />
                  ) : (
                    <select
                      value={formData.awayTeam}
                      onChange={(e) => handleChange('awayTeam', e.target.value)}
                      className="input pl-10"
                      required
                    >
                      <option value="">Sélectionnez l'équipe visiteur</option>
                      {commonOpponents.map(team => (
                        <option key={team} value={team}>{team}</option>
                      ))}
                    </select>
                  )}
                </div>
              </div>
            </div>

            {/* Match Preview */}
            {formData.homeTeam && formData.awayTeam && (
              <div className="mt-4 p-4 bg-green-50 rounded-lg">
                <div className="text-center">
                  <h4 className="text-lg font-semibold text-green-800">
                    {formData.homeTeam} vs {formData.awayTeam}
                  </h4>
                  {formatDateTime() && (
                    <p className="text-sm text-green-600 mt-1">{formatDateTime()}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Date and Time */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Date et heure</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date du match *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="date"
                    value={formData.matchDate}
                    onChange={(e) => handleChange('matchDate', e.target.value)}
                    className="input pl-10"
                    required
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Heure du match *
                </label>
                <div className="relative">
                  <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="time"
                    value={formData.matchTime}
                    onChange={(e) => handleChange('matchTime', e.target.value)}
                    className="input pl-10"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Location and Competition */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Lieu et compétition</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Lieu du match *
                </label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <select
                    value={formData.location}
                    onChange={(e) => handleChange('location', e.target.value)}
                    className="input pl-10"
                    required
                  >
                    {venues.map(venue => (
                      <option key={venue} value={venue}>{venue}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Compétition *
                </label>
                <div className="relative">
                  <Trophy className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <select
                    value={formData.competition}
                    onChange={(e) => handleChange('competition', e.target.value)}
                    className="input pl-10"
                    required
                  >
                    {competitions.map(comp => (
                      <option key={comp} value={comp}>{comp}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Ticketing */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Billetterie</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Prix du billet (DT)
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="number"
                    min="0"
                    step="0.5"
                    value={formData.ticketPrice}
                    onChange={(e) => handleChange('ticketPrice', parseFloat(e.target.value))}
                    className="input pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Billets disponibles
                </label>
                <div className="relative">
                  <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="number"
                    min="0"
                    value={formData.availableTickets}
                    onChange={(e) => handleChange('availableTickets', parseInt(e.target.value))}
                    className="input pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Revenue Estimation */}
            {formData.ticketPrice > 0 && formData.availableTickets > 0 && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Revenus potentiels :</strong> {(formData.ticketPrice * formData.availableTickets).toLocaleString()} DT
                  (si tous les billets sont vendus)
                </p>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Description</h3>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description du match
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                rows={4}
                className="input"
                placeholder="Ajoutez des informations supplémentaires sur ce match (enjeux, contexte, etc.)..."
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pb-8">
            <button
              type="button"
              onClick={() => navigate('/dashboard/matches')}
              className="btn btn-outline"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Créer le match
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default AddMatch;
