const { PrismaClient } = require('@prisma/client');
const { faker } = require('@faker-js/faker');
const bcrypt = require('bcryptjs');
const fs = require('fs');
const path = require('path');
const https = require('https');

const prisma = new PrismaClient();

// Configuration
const CONFIG = {
  PLAYERS_COUNT: 30,
  TEAMS_COUNT: 10,
  MATCHES_COUNT: 15,
  NEWS_COUNT: 25,
  ADMIN_COUNT: 3
};

// URLs d'images par défaut
const DEFAULT_IMAGES = {
  players: [
    'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=400&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=400&h=400&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face'
  ],
  teams: [
    'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=300&h=300&fit=crop',
    'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=300&h=300&fit=crop',
    'https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=300&h=300&fit=crop'
  ],
  news: [
    'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1522778119026-d647f0596c20?w=800&h=400&fit=crop',
    'https://images.unsplash.com/photo-1489944440615-453fc2b6a9a9?w=800&h=400&fit=crop'
  ]
};

// Noms d'équipes tunisiennes
const TUNISIAN_TEAMS = [
  'Espérance Sportive de Tunis',
  'Club Africain',
  'Étoile Sportive du Sahel',
  'Club Sportif Sfaxien',
  'Union Sportive Monastirienne',
  'Club Athlétique Bizertin',
  'Olympique de Béja',
  'Avenir Sportif de La Marsa',
  'Stade Tunisien',
  'Union Sportive Ben Guerdane'
];

// Positions de football
const POSITIONS = ['GOALKEEPER', 'DEFENDER', 'MIDFIELDER', 'FORWARD'];
const PREFERRED_FOOT = ['LEFT', 'RIGHT', 'BOTH'];

// Fonction pour télécharger une image
async function downloadImage(url, filename) {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filename);
    https.get(url, (response) => {
      response.pipe(file);
      file.on('finish', () => {
        file.close();
        resolve(filename);
      });
    }).on('error', (err) => {
      fs.unlink(filename, () => {}); // Supprimer le fichier en cas d'erreur
      reject(err);
    });
  });
}

// Fonction pour créer le dossier d'images s'il n'existe pas
function ensureImageDirectory() {
  const imageDir = path.join(__dirname, '../public/images');
  const playersDir = path.join(imageDir, 'players');
  const teamsDir = path.join(imageDir, 'teams');
  const newsDir = path.join(imageDir, 'news');

  [imageDir, playersDir, teamsDir, newsDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });

  return { imageDir, playersDir, teamsDir, newsDir };
}

// Générer un utilisateur admin
async function createAdmin(index) {
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  return {
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: `admin${index}@esc.tn`,
    password: hashedPassword,
    role: 'ADMIN',
    phone: faker.phone.number('+216 ## ### ###'),
    avatar: faker.helpers.arrayElement(DEFAULT_IMAGES.players)
  };
}

// Générer un joueur avec utilisateur
async function createPlayer(index, dirs) {
  const hashedPassword = await bcrypt.hash('player123', 10);
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();
  
  // Télécharger une image de joueur
  let profileImage = faker.helpers.arrayElement(DEFAULT_IMAGES.players);
  try {
    const imageUrl = faker.helpers.arrayElement(DEFAULT_IMAGES.players);
    const imageName = `player_${index}.jpg`;
    const imagePath = path.join(dirs.playersDir, imageName);
    await downloadImage(imageUrl, imagePath);
    profileImage = `/images/players/${imageName}`;
  } catch (error) {
    console.log(`Erreur téléchargement image joueur ${index}:`, error.message);
  }

  const user = {
    firstName,
    lastName,
    email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@esc.tn`,
    password: hashedPassword,
    role: 'PLAYER',
    phone: faker.phone.number('+216 ## ### ###'),
    avatar: profileImage
  };

  const player = {
    dateOfBirth: faker.date.between({ 
      from: new Date('1990-01-01'), 
      to: new Date('2005-12-31') 
    }),
    nationality: faker.helpers.arrayElement(['Tunisie', 'Algérie', 'Maroc', 'France', 'Sénégal']),
    position: faker.helpers.arrayElement(POSITIONS),
    jerseyNumber: index + 1,
    height: faker.number.int({ min: 165, max: 195 }),
    weight: faker.number.int({ min: 60, max: 90 }),
    preferredFoot: faker.helpers.arrayElement(PREFERRED_FOOT),
    contractStart: faker.date.between({ 
      from: new Date('2023-01-01'), 
      to: new Date('2024-06-30') 
    }),
    contractEnd: faker.date.between({ 
      from: new Date('2025-01-01'), 
      to: new Date('2027-12-31') 
    }),
    salary: faker.number.int({ min: 2000, max: 15000 }),
    biography: faker.lorem.paragraph(3),
    previousClubs: faker.helpers.arrayElements(TUNISIAN_TEAMS, 2).join(', '),
    achievements: faker.lorem.sentence(),
    profileImage,
    isActive: faker.datatype.boolean(0.9) // 90% actifs
  };

  return { user, player };
}

// Générer une équipe
async function createTeam(index, dirs) {
  const teamName = TUNISIAN_TEAMS[index] || `Équipe ${index + 1}`;
  
  // Télécharger une image d'équipe
  let logo = faker.helpers.arrayElement(DEFAULT_IMAGES.teams);
  try {
    const imageUrl = faker.helpers.arrayElement(DEFAULT_IMAGES.teams);
    const imageName = `team_${index}.jpg`;
    const imagePath = path.join(dirs.teamsDir, imageName);
    await downloadImage(imageUrl, imagePath);
    logo = `/images/teams/${imageName}`;
  } catch (error) {
    console.log(`Erreur téléchargement logo équipe ${index}:`, error.message);
  }

  return {
    name: teamName,
    logo,
    city: faker.location.city(),
    stadium: `Stade ${faker.location.streetName()}`,
    foundedYear: faker.number.int({ min: 1920, max: 2000 }),
    description: faker.lorem.paragraph(2),
    website: `https://www.${teamName.toLowerCase().replace(/\s+/g, '')}.tn`,
    isActive: true
  };
}

// Générer un match
function createMatch(teams) {
  const homeTeam = faker.helpers.arrayElement(teams);
  let awayTeam = faker.helpers.arrayElement(teams);
  
  // S'assurer que l'équipe visiteur est différente de l'équipe domicile
  while (awayTeam.id === homeTeam.id) {
    awayTeam = faker.helpers.arrayElement(teams);
  }

  const matchDate = faker.date.between({ 
    from: new Date('2024-09-01'), 
    to: new Date('2025-05-31') 
  });

  const isPlayed = matchDate < new Date();
  
  return {
    homeTeamId: homeTeam.id,
    awayTeamId: awayTeam.id,
    matchDate,
    venue: homeTeam.stadium || `Stade ${faker.location.streetName()}`,
    homeScore: isPlayed ? faker.number.int({ min: 0, max: 5 }) : null,
    awayScore: isPlayed ? faker.number.int({ min: 0, max: 5 }) : null,
    status: isPlayed ? 'FINISHED' : faker.helpers.arrayElement(['SCHEDULED', 'LIVE']),
    season: '2024-2025',
    competition: faker.helpers.arrayElement(['Championnat', 'Coupe de Tunisie', 'Ligue des Champions']),
    description: faker.lorem.sentence()
  };
}

// Générer un article de news
async function createNews(index, dirs) {
  // Télécharger une image d'article
  let featuredImage = faker.helpers.arrayElement(DEFAULT_IMAGES.news);
  try {
    const imageUrl = faker.helpers.arrayElement(DEFAULT_IMAGES.news);
    const imageName = `news_${index}.jpg`;
    const imagePath = path.join(dirs.newsDir, imageName);
    await downloadImage(imageUrl, imagePath);
    featuredImage = `/images/news/${imageName}`;
  } catch (error) {
    console.log(`Erreur téléchargement image article ${index}:`, error.message);
  }

  return {
    title: faker.lorem.sentence({ min: 5, max: 10 }),
    content: faker.lorem.paragraphs(5, '\n\n'),
    excerpt: faker.lorem.paragraph(),
    featuredImage,
    category: faker.helpers.arrayElement(['Match', 'Transfert', 'Formation', 'Club', 'Communauté']),
    tags: faker.helpers.arrayElements(['football', 'esc', 'tunisie', 'sport', 'équipe', 'victoire'], 3).join(','),
    isPublished: faker.datatype.boolean(0.8), // 80% publiés
    publishedAt: faker.date.between({ 
      from: new Date('2024-01-01'), 
      to: new Date() 
    })
  };
}

// Fonction principale de seed
async function seedDatabase() {
  try {
    console.log('🚀 Démarrage du seed de la base de données...');
    
    // Créer les dossiers d'images
    const dirs = ensureImageDirectory();
    console.log('📁 Dossiers d\'images créés');

    // Nettoyer la base de données
    console.log('🧹 Nettoyage de la base de données...');
    await prisma.playerStats.deleteMany();
    await prisma.player.deleteMany();
    await prisma.match.deleteMany();
    await prisma.team.deleteMany();
    await prisma.news.deleteMany();
    await prisma.user.deleteMany();

    // Créer les admins
    console.log('👑 Création des administrateurs...');
    for (let i = 0; i < CONFIG.ADMIN_COUNT; i++) {
      const adminData = await createAdmin(i + 1);
      await prisma.user.create({ data: adminData });
      console.log(`✅ Admin créé: ${adminData.email}`);
    }

    // Créer les équipes
    console.log('🏆 Création des équipes...');
    const teams = [];
    for (let i = 0; i < CONFIG.TEAMS_COUNT; i++) {
      const teamData = await createTeam(i, dirs);
      const team = await prisma.team.create({ data: teamData });
      teams.push(team);
      console.log(`✅ Équipe créée: ${team.name}`);
    }

    // Créer les joueurs
    console.log('⚽ Création des joueurs...');
    for (let i = 0; i < CONFIG.PLAYERS_COUNT; i++) {
      const { user: userData, player: playerData } = await createPlayer(i, dirs);
      
      const user = await prisma.user.create({ data: userData });
      const player = await prisma.player.create({
        data: {
          ...playerData,
          userId: user.id
        }
      });
      
      console.log(`✅ Joueur créé: ${user.firstName} ${user.lastName} (#${player.jerseyNumber})`);
    }

    // Créer les matchs
    console.log('🏟️ Création des matchs...');
    for (let i = 0; i < CONFIG.MATCHES_COUNT; i++) {
      const matchData = createMatch(teams);
      const match = await prisma.match.create({ data: matchData });
      console.log(`✅ Match créé: ${match.homeTeamId} vs ${match.awayTeamId}`);
    }

    // Créer les articles de news
    console.log('📰 Création des articles...');
    for (let i = 0; i < CONFIG.NEWS_COUNT; i++) {
      const newsData = await createNews(i, dirs);
      const news = await prisma.news.create({ data: newsData });
      console.log(`✅ Article créé: ${news.title.substring(0, 50)}...`);
    }

    console.log('\n🎉 Seed terminé avec succès !');
    console.log(`📊 Données créées:`);
    console.log(`   - ${CONFIG.ADMIN_COUNT} administrateurs`);
    console.log(`   - ${CONFIG.PLAYERS_COUNT} joueurs`);
    console.log(`   - ${CONFIG.TEAMS_COUNT} équipes`);
    console.log(`   - ${CONFIG.MATCHES_COUNT} matchs`);
    console.log(`   - ${CONFIG.NEWS_COUNT} articles`);
    console.log(`\n🔑 Comptes de test:`);
    console.log(`   Admin: <EMAIL> / admin123`);
    console.log(`   Admin: <EMAIL> / admin123`);
    console.log(`   Admin: <EMAIL> / admin123`);

  } catch (error) {
    console.error('❌ Erreur lors du seed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  seedDatabase()
    .then(() => {
      console.log('✅ Script terminé');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur fatale:', error);
      process.exit(1);
    });
}

module.exports = { seedDatabase };
