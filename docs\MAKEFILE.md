# Guide Makefile - Espoir Sportive Chorbane

## 📋 Vue d'ensemble

Le Makefile fournit une interface simplifiée pour gérer le projet Espoir Sportive Chorbane avec des commandes courtes et mémorisables.

## 🚀 Commandes Principales

### Installation et Configuration

```bash
make install        # Installer les dépendances localement
make setup          # Configuration initiale du projet
```

**Détails :**
- `install` : Installe npm packages pour backend et frontend
- `setup` : Crée les dossiers nécessaires, copie .env, rend les scripts exécutables

### Développement

```bash
make dev            # Démarrer en mode développement
make dev-logs       # Voir les logs en développement
make dev-stop       # Arrêter les services de développement
make restart-dev    # Redémarrer en développement
```

**Services démarrés en dev :**
- Frontend : http://localhost:3001
- Backend : http://localhost:5001
- PostgreSQL : localhost:5433
- Adminer : http://localhost:8081
- MailHog : http://localhost:8025
- Redis : localhost:6380

### Production

```bash
make prod           # Démarrer en mode production
make prod-logs      # Voir les logs en production
make prod-stop      # Arrêter les services de production
make restart-prod   # Redémarrer en production
```

**Services démarrés en prod :**
- Application : http://localhost:3000
- API : http://localhost:5000
- PostgreSQL : localhost:5432

### Gestion Générale

```bash
make stop           # Arrêter tous les services (dev + prod)
make logs           # Voir les logs (développement par défaut)
make status         # Afficher le statut des services
make health         # Vérifier la santé des services
```

## 🗄️ Base de Données

### Migrations et Génération

```bash
make db-migrate     # Exécuter les migrations Prisma
make db-generate    # Générer le client Prisma
make db-studio      # Ouvrir Prisma Studio
```

### Seed et Reset

```bash
make db-seed                # Seed basique
make db-seed-advanced       # Seed avec Faker (recommandé)
make db-reset               # Reset + seed basique
make db-reset-advanced      # Reset + seed Faker (recommandé)
```

### Génération de Données avec Faker

```bash
make db-generate-articles   # Générer 10 articles
make db-generate-matches    # Générer 5 matchs
make db-generate-supporters # Générer 20 supporters
make db-generate-comments   # Générer des commentaires
make db-generate-events     # Générer des événements de match
make db-generate-all        # Générer un peu de tout
```

### Sauvegarde

```bash
make db-backup      # Sauvegarder la base de données
```

## 🐳 Docker

### Construction et Images

```bash
make build          # Construire les images Docker
make docker-build   # Construire sans cache
make docker-pull    # Mettre à jour les images de base
make docker-prune   # Nettoyer les images inutilisées
```

### Accès aux Conteneurs

```bash
make shell-backend  # Accéder au shell du backend
make shell-frontend # Accéder au shell du frontend
make shell-db       # Accéder au shell PostgreSQL
```

## 📊 Logs et Monitoring

### Logs Spécifiques

```bash
make logs           # Tous les logs (dev)
make logs-backend   # Logs du backend uniquement
make logs-frontend  # Logs du frontend uniquement
make logs-db        # Logs de la base de données
```

### Monitoring

```bash
make status         # Statut des conteneurs
make stats          # Statistiques des conteneurs
make health         # Vérification de santé
```

**Exemple de sortie `make health` :**
```
✅ Backend Dev OK
✅ Frontend Dev OK
❌ Backend Prod KO
❌ Frontend Prod KO
```

## 🧹 Nettoyage

### Nettoyage Léger

```bash
make clean          # Nettoyer conteneurs arrêtés et images
```

### Nettoyage Complet

```bash
make clean-all      # Supprime TOUT (avec confirmation)
make clean-volumes  # Supprimer les volumes (perte de données)
```

**⚠️ Attention :** `clean-all` et `clean-volumes` suppriment définitivement les données !

## 🛠️ Développement

### Tests et Qualité

```bash
make test           # Exécuter les tests
make lint           # Vérifier la qualité du code
make format         # Formater le code
```

### Maintenance

```bash
make update         # Mettre à jour les dépendances
make clean          # Nettoyage des fichiers temporaires
```

## 📚 Documentation

```bash
make docs           # Afficher la liste des documentations
```

**Documentations disponibles :**
- README.md - Documentation principale
- docs/INSTALLATION.md - Guide d'installation
- docs/TECHNICAL_DOCUMENTATION.md - Documentation technique
- docs/DOCKER.md - Guide Docker
- docs/FAKER_DATA.md - Guide Faker
- CHANGELOG.md - Journal des modifications

## 🔧 Git et Versioning

```bash
make commit         # Faire un commit interactif
make push           # Push vers le repository
make pull           # Pull depuis le repository
```

**Exemple d'utilisation :**
```bash
make commit
# Prompt: Message du commit: feat: nouvelle fonctionnalité
# Exécute: git add . && git commit -m "feat: nouvelle fonctionnalité"

make push
# Exécute: git push origin main
```

## 📋 Workflows Recommandés

### Démarrage Initial

```bash
# 1. Configuration
make setup

# 2. Démarrage en développement
make dev

# 3. Seed avec données réalistes
make db-seed-advanced

# 4. Vérifier que tout fonctionne
make health
```

### Développement Quotidien

```bash
# Démarrer la journée
make dev
make logs

# Ajouter des données de test
make db-generate-articles
make db-generate-matches

# Fin de journée
make stop
```

### Mise en Production

```bash
# 1. Tests
make test
make lint

# 2. Build
make docker-build

# 3. Démarrage production
make prod

# 4. Vérification
make health
make prod-logs
```

### Maintenance

```bash
# Mise à jour
make update
make docker-pull

# Nettoyage
make clean
make docker-prune

# Sauvegarde
make db-backup
```

## 🔍 Dépannage

### Problèmes Courants

#### Services qui ne démarrent pas
```bash
make status         # Voir l'état
make logs          # Voir les erreurs
make clean         # Nettoyer
make dev           # Redémarrer
```

#### Base de données corrompue
```bash
make db-reset-advanced  # Reset complet
```

#### Images Docker obsolètes
```bash
make docker-pull    # Mettre à jour les images
make docker-build   # Reconstruire
```

#### Espace disque plein
```bash
make clean-all      # Nettoyage complet (avec confirmation)
```

### Variables d'Environnement

Le Makefile utilise ces variables par défaut :
- `DOCKER_COMPOSE_DEV` : docker-compose -f docker-compose.dev.yml
- `DOCKER_COMPOSE_PROD` : docker-compose
- `PROJECT_NAME` : espoir-sportive-chorbane

## 📖 Aide

```bash
make help           # Afficher l'aide complète
make                # Afficher l'aide (commande par défaut)
```

**Exemple de sortie :**
```
Espoir Sportive Chorbane - Commandes disponibles:

  dev             Démarrer en mode développement
  prod            Démarrer en mode production
  stop            Arrêter tous les services
  db-seed-advanced Peupler avec des données Faker réalistes
  logs            Voir les logs
  clean           Nettoyage léger
  help            Afficher cette aide

Exemples:
  make dev          # Démarrer en développement
  make prod         # Démarrer en production
  make logs         # Voir les logs
  make stop         # Arrêter tous les services
```

---

*Documentation Makefile mise à jour le 24/05/2025*
