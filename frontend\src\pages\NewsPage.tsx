import React from 'react';
import { Calendar, User, ArrowRight } from 'lucide-react';

const NewsPage = () => {
  const articles = [
    {
      id: 1,
      title: 'Victoire éclatante contre le CA Bizertin',
      excerpt: 'Une performance remarquable de nos joueurs qui ont dominé le match de bout en bout.',
      category: 'Match',
      author: 'Rédaction ES',
      date: '2025-05-21',
      image: 'https://images.unsplash.com/photo-1574629810360-7efbbe195018?w=800&h=400&fit=crop',
    },
    {
      id: 2,
      title: 'Nouveau renfort : Arrivée de Montassar Me<PERSON>',
      excerpt: 'Le défenseur international rejoint nos rangs pour renforcer notre ligne défensive.',
      category: 'Transfert',
      author: 'Direction Sportive',
      date: '2025-05-18',
      image: 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=800&h=400&fit=crop',
    },
    {
      id: 3,
      title: 'Préparation intensive pour la nouvelle saison',
      excerpt: 'Nos joueurs s\'entraînent dur pour aborder la prochaine saison dans les meilleures conditions.',
      category: 'Entraînement',
      author: 'Staff Technique',
      date: '2025-05-15',
      image: 'https://images.unsplash.com/photo-1431324155629-1a6deb1dec8d?w=800&h=400&fit=crop',
    },
    {
      id: 4,
      title: 'Inauguration de la nouvelle académie',
      excerpt: 'Un nouveau centre de formation pour développer les talents de demain.',
      category: 'Club',
      author: 'Direction',
      date: '2025-05-12',
      image: 'https://images.unsplash.com/photo-1459865264687-595d652de67e?w=800&h=400&fit=crop',
    },
  ];

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Match':
        return 'bg-green-100 text-green-800';
      case 'Transfert':
        return 'bg-blue-100 text-blue-800';
      case 'Entraînement':
        return 'bg-yellow-100 text-yellow-800';
      case 'Club':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="gradient-hero text-white section-padding">
        <div className="container-custom text-center">
          <h1 className="hero-title mb-6">Actualités</h1>
          <p className="hero-subtitle mx-auto">
            Toute l'actualité d'Espoir Sportive Chorbane : matchs, transferts, et vie du club
          </p>
        </div>
      </section>

      {/* Articles Grid */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Main Article */}
            <div className="lg:col-span-2">
              <article className="card card-hover overflow-hidden mb-8">
                <div className="aspect-video bg-gradient-to-br from-primary-400 to-primary-600 relative overflow-hidden">
                  <img 
                    src={articles[0].image} 
                    alt={articles[0].title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(articles[0].category)}`}>
                      {articles[0].category}
                    </span>
                  </div>
                </div>
                <div className="p-8">
                  <h2 className="text-2xl md:text-3xl font-bold text-dark-900 mb-4">
                    {articles[0].title}
                  </h2>
                  <p className="text-gray-600 mb-6 text-lg">
                    {articles[0].excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <div className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{articles[0].author}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{formatDate(articles[0].date)}</span>
                      </div>
                    </div>
                    <button className="btn btn-primary btn-sm">
                      Lire l'article
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </button>
                  </div>
                </div>
              </article>

              {/* Other Articles */}
              <div className="space-y-6">
                {articles.slice(1).map((article) => (
                  <article key={article.id} className="card card-hover">
                    <div className="md:flex">
                      <div className="md:w-1/3">
                        <div className="aspect-video md:aspect-square bg-gradient-to-br from-primary-400 to-primary-600 relative overflow-hidden">
                          <img 
                            src={article.image} 
                            alt={article.title}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-2 left-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(article.category)}`}>
                              {article.category}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="md:w-2/3 p-6">
                        <h3 className="text-xl font-semibold text-dark-900 mb-3">
                          {article.title}
                        </h3>
                        <p className="text-gray-600 mb-4">
                          {article.excerpt}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center space-x-1">
                              <User className="w-4 h-4" />
                              <span>{article.author}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="w-4 h-4" />
                              <span>{formatDate(article.date)}</span>
                            </div>
                          </div>
                          <button className="text-primary-600 hover:text-primary-700 font-medium text-sm">
                            Lire plus →
                          </button>
                        </div>
                      </div>
                    </div>
                  </article>
                ))}
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-8">
              {/* Categories */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-dark-900 mb-4">Catégories</h3>
                <div className="space-y-2">
                  {['Tous', 'Match', 'Transfert', 'Entraînement', 'Club'].map((category) => (
                    <button
                      key={category}
                      className="block w-full text-left px-3 py-2 rounded-lg text-gray-600 hover:bg-gray-100 hover:text-primary-600 transition-colors"
                    >
                      {category}
                    </button>
                  ))}
                </div>
              </div>

              {/* Newsletter */}
              <div className="card p-6 gradient-primary text-white">
                <h3 className="text-lg font-semibold mb-4">Newsletter</h3>
                <p className="text-sm mb-4 text-blue-100">
                  Recevez toutes les actualités du club directement dans votre boîte mail.
                </p>
                <div className="space-y-3">
                  <input
                    type="email"
                    placeholder="Votre email"
                    className="w-full px-3 py-2 rounded-lg text-dark-900 focus:outline-none focus:ring-2 focus:ring-white"
                  />
                  <button className="btn btn-secondary btn-sm w-full">
                    S'abonner
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default NewsPage;
