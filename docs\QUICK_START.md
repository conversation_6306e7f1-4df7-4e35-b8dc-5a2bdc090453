# Guide de Démarrage Rapide - Espoir Sportive Chorbane

## 🚀 Démarrage en 5 Minutes

### Prérequis
- Docker et Docker Compose installés
- Git installé

### Installation Express

```bash
# 1. <PERSON><PERSON><PERSON> le projet
git clone <repository-url>
cd espoir-sportive-chorbane

# 2. Démarrage automatique (Linux/macOS)
chmod +x scripts/docker-start.sh
./scripts/docker-start.sh dev

# Ou avec Make
make setup && make dev

# Ou Windows PowerShell
.\scripts\docker-start.ps1 dev
```

### Accès aux Services

Une fois démarré, accédez à :

- **Frontend** : http://localhost:3001
- **Backend API** : http://localhost:5001
- **Base de données (Adminer)** : http://localhost:8081
- **Email de test (MailHog)** : http://localhost:8025

### Comptes de Test

```
Admin     : <EMAIL> / admin123
Joueur    : <EMAIL> / player123
Supporter : <EMAIL> / supporter123
```

## 📚 Documentation Rapide

### Liens Essentiels

| Document | Description | Lien |
|----------|-------------|------|
| 📖 **README** | Documentation principale | [README.md](../README.md) |
| 🐳 **Docker** | Guide Docker complet | [DOCKER.md](DOCKER.md) |
| 🎭 **Faker** | Données de test réalistes | [FAKER_DATA.md](FAKER_DATA.md) |
| 🔧 **Installation** | Guide d'installation détaillé | [INSTALLATION.md](INSTALLATION.md) |
| ⚙️ **Makefile** | Commandes Make | [MAKEFILE.md](MAKEFILE.md) |
| 🏗️ **Technique** | Architecture et API | [TECHNICAL_DOCUMENTATION.md](TECHNICAL_DOCUMENTATION.md) |
| 📋 **Fonctionnalités** | Résumé complet | [FEATURES_SUMMARY.md](FEATURES_SUMMARY.md) |
| 📝 **Changelog** | Historique des versions | [../CHANGELOG.md](../CHANGELOG.md) |

### Commandes Essentielles

```bash
# Démarrage
make dev                    # Développement
make prod                   # Production
make stop                   # Arrêter

# Base de données
make db-seed-advanced       # Données réalistes avec Faker
make db-studio              # Interface graphique DB

# Logs et monitoring
make logs                   # Voir les logs
make health                 # Vérifier la santé
make status                 # Statut des services

# Aide
make help                   # Liste des commandes
```

## 🎯 Cas d'Usage Rapides

### Développeur Backend
```bash
# Démarrer l'environnement
make dev

# Peupler avec des données réalistes
make db-seed-advanced

# Tester l'API
curl http://localhost:5001/api/health

# Voir les logs
make logs-backend
```

### Développeur Frontend
```bash
# Démarrer l'environnement
make dev

# Accéder au frontend
open http://localhost:3001

# Voir les logs frontend
make logs-frontend
```

### Administrateur/DevOps
```bash
# Démarrer en production
make prod

# Vérifier la santé
make health

# Voir les statistiques
make stats

# Sauvegarde
make db-backup
```

### Testeur/QA
```bash
# Environnement de test
make dev

# Générer des données de test
make db-generate-articles
make db-generate-matches

# Accéder à l'interface DB
open http://localhost:8081
```

## 🔧 Résolution Rapide de Problèmes

### Problème : Services ne démarrent pas
```bash
make status                 # Voir l'état
make logs                   # Voir les erreurs
make clean && make dev      # Nettoyer et redémarrer
```

### Problème : Base de données corrompue
```bash
make db-reset-advanced      # Reset complet avec données Faker
```

### Problème : Port déjà utilisé
```bash
# Modifier les ports dans docker-compose.dev.yml
# Ou arrêter les autres services
make stop
```

### Problème : Espace disque plein
```bash
make clean-all              # Nettoyage complet (avec confirmation)
```

## 📊 API Endpoints Principaux

### Authentification
```bash
# Login
curl -X POST http://localhost:5001/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}'

# Profil utilisateur
curl -H "Authorization: Bearer <token>" \
  http://localhost:5001/api/auth/me
```

### Données
```bash
# Liste des joueurs
curl http://localhost:5001/api/players

# Statistiques équipe
curl -H "Authorization: Bearer <token>" \
  http://localhost:5001/api/stats/team

# Articles publiés
curl http://localhost:5001/api/articles
```

## 🎭 Données de Test Faker

### Génération Rapide
```bash
# Seed complet avec Faker (recommandé)
make db-seed-advanced

# Générer des données spécifiques
make db-generate-articles   # 10 articles
make db-generate-matches    # 5 matchs
make db-generate-supporters # 20 supporters
```

### Données Générées
- **28 joueurs** avec noms tunisiens réalistes
- **25 supporters** avec profils variés
- **25 matchs** avec scores authentiques
- **30 entraînements** avec présences (88% taux)
- **Articles** avec titres contextuels
- **Statistiques** sur 3 saisons

## 🔍 Debugging Rapide

### Logs en Temps Réel
```bash
make logs                   # Tous les services
make logs-backend          # Backend uniquement
make logs-frontend         # Frontend uniquement
make logs-db               # Base de données
```

### Accès aux Conteneurs
```bash
make shell-backend         # Shell dans le backend
make shell-frontend        # Shell dans le frontend
make shell-db              # Shell PostgreSQL
```

### Vérifications de Santé
```bash
make health                # Vérifier tous les services
make status                # Statut des conteneurs
make stats                 # Utilisation des ressources
```

## 📱 Interfaces Web

### Développement
- **Application** : http://localhost:3001
- **API Health** : http://localhost:5001/api/health
- **Adminer (DB)** : http://localhost:8081
  - Serveur : `postgres-dev`
  - Utilisateur : `espoir_dev`
  - Mot de passe : `dev_password`
  - Base : `espoir_sportive_dev`
- **MailHog** : http://localhost:8025

### Production
- **Application** : http://localhost:3000
- **API** : http://localhost:5000

## 🎯 Prochaines Étapes

Après le démarrage rapide :

1. **Explorer l'API** avec Adminer ou curl
2. **Consulter la documentation technique** pour comprendre l'architecture
3. **Générer plus de données** avec les scripts Faker
4. **Développer le frontend** React (prochaine phase)

## 📞 Support

- **Documentation** : Voir les liens ci-dessus
- **Issues** : Créer une issue GitHub
- **Email** : <EMAIL>

---

*Guide de démarrage rapide - Dernière mise à jour : 24/05/2025*
