const express = require('express');
const { body, validationResult } = require('express-validator');
const { prisma } = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// GET /api/articles - Récupérer tous les articles (public pour les articles publiés)
router.get('/', async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      published = 'true', 
      search,
      tag 
    } = req.query;
    const skip = (page - 1) * limit;

    // Construire les filtres
    const where = {};
    
    // Si pas authentifié ou pas admin, ne montrer que les articles publiés
    if (!req.user || req.user.role !== 'ADMIN') {
      where.isPublished = true;
    } else if (published !== 'all') {
      where.isPublished = published === 'true';
    }

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { content: { contains: search, mode: 'insensitive' } },
        { excerpt: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (tag) {
      where.tags = {
        some: {
          tag: {
            name: { equals: tag, mode: 'insensitive' }
          }
        }
      };
    }

    const [articles, total] = await Promise.all([
      prisma.article.findMany({
        where,
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          author: {
            select: {
              firstName: true,
              lastName: true,
              avatar: true
            }
          },
          tags: {
            include: {
              tag: true
            }
          },
          comments: {
            select: {
              id: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      prisma.article.count({ where })
    ]);

    // Transformer les données pour inclure le nombre de commentaires
    const articlesWithCommentCount = articles.map(article => ({
      ...article,
      commentCount: article.comments.length,
      comments: undefined // Retirer les commentaires de la réponse
    }));

    res.json({
      message: 'Articles récupérés avec succès',
      articles: articlesWithCommentCount,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des articles:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/articles/:id - Récupérer un article par ID (public si publié)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const article = await prisma.article.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        tags: {
          include: {
            tag: true
          }
        },
        comments: {
          include: {
            author: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        }
      }
    });

    if (!article) {
      return res.status(404).json({
        message: 'Article non trouvé'
      });
    }

    // Vérifier si l'article est publié ou si l'utilisateur est admin
    if (!article.isPublished && (!req.user || req.user.role !== 'ADMIN')) {
      return res.status(403).json({
        message: 'Article non publié'
      });
    }

    res.json({
      message: 'Article récupéré avec succès',
      article
    });

  } catch (error) {
    console.error('Erreur lors de la récupération de l\'article:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// POST /api/articles - Créer un nouvel article (Admin seulement)
router.post('/', [
  body('title')
    .notEmpty()
    .withMessage('Titre de l\'article requis'),
  body('content')
    .notEmpty()
    .withMessage('Contenu de l\'article requis'),
  body('excerpt')
    .optional()
    .isLength({ max: 300 })
    .withMessage('L\'extrait ne peut pas dépasser 300 caractères'),
  body('isPublished')
    .optional()
    .isBoolean()
    .withMessage('Statut de publication invalide'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Les tags doivent être un tableau')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { 
      title, 
      content, 
      excerpt, 
      coverImage, 
      isPublished = false, 
      tags = [] 
    } = req.body;

    // Créer l'article
    const article = await prisma.article.create({
      data: {
        title,
        content,
        excerpt,
        coverImage,
        isPublished,
        authorId: req.user.id
      }
    });

    // Gérer les tags
    if (tags.length > 0) {
      for (const tagName of tags) {
        // Créer le tag s'il n'existe pas
        const tag = await prisma.tag.upsert({
          where: { name: tagName },
          update: {},
          create: { name: tagName }
        });

        // Associer le tag à l'article
        await prisma.articleTag.create({
          data: {
            articleId: article.id,
            tagId: tag.id
          }
        });
      }
    }

    // Récupérer l'article complet avec les relations
    const articleWithRelations = await prisma.article.findUnique({
      where: { id: article.id },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        tags: {
          include: {
            tag: true
          }
        }
      }
    });

    res.status(201).json({
      message: 'Article créé avec succès',
      article: articleWithRelations
    });

  } catch (error) {
    console.error('Erreur lors de la création de l\'article:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// PUT /api/articles/:id - Mettre à jour un article (Admin seulement)
router.put('/:id', [
  body('title')
    .optional()
    .notEmpty()
    .withMessage('Titre de l\'article requis'),
  body('content')
    .optional()
    .notEmpty()
    .withMessage('Contenu de l\'article requis'),
  body('excerpt')
    .optional()
    .isLength({ max: 300 })
    .withMessage('L\'extrait ne peut pas dépasser 300 caractères'),
  body('isPublished')
    .optional()
    .isBoolean()
    .withMessage('Statut de publication invalide'),
  body('tags')
    .optional()
    .isArray()
    .withMessage('Les tags doivent être un tableau')
], authenticateToken, requireAdmin, async (req, res) => {
  try {
    // Vérifier les erreurs de validation
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        message: 'Erreurs de validation',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const { 
      title, 
      content, 
      excerpt, 
      coverImage, 
      isPublished, 
      tags 
    } = req.body;

    // Vérifier que l'article existe
    const existingArticle = await prisma.article.findUnique({
      where: { id }
    });

    if (!existingArticle) {
      return res.status(404).json({
        message: 'Article non trouvé'
      });
    }

    // Mettre à jour l'article
    const updatedArticle = await prisma.article.update({
      where: { id },
      data: {
        title,
        content,
        excerpt,
        coverImage,
        isPublished
      }
    });

    // Gérer les tags si fournis
    if (tags !== undefined) {
      // Supprimer les anciens tags
      await prisma.articleTag.deleteMany({
        where: { articleId: id }
      });

      // Ajouter les nouveaux tags
      for (const tagName of tags) {
        const tag = await prisma.tag.upsert({
          where: { name: tagName },
          update: {},
          create: { name: tagName }
        });

        await prisma.articleTag.create({
          data: {
            articleId: id,
            tagId: tag.id
          }
        });
      }
    }

    // Récupérer l'article complet avec les relations
    const articleWithRelations = await prisma.article.findUnique({
      where: { id },
      include: {
        author: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true
          }
        },
        tags: {
          include: {
            tag: true
          }
        }
      }
    });

    res.json({
      message: 'Article mis à jour avec succès',
      article: articleWithRelations
    });

  } catch (error) {
    console.error('Erreur lors de la mise à jour de l\'article:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// DELETE /api/articles/:id - Supprimer un article (Admin seulement)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier que l'article existe
    const existingArticle = await prisma.article.findUnique({
      where: { id }
    });

    if (!existingArticle) {
      return res.status(404).json({
        message: 'Article non trouvé'
      });
    }

    await prisma.article.delete({
      where: { id }
    });

    res.json({
      message: 'Article supprimé avec succès'
    });

  } catch (error) {
    console.error('Erreur lors de la suppression de l\'article:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
