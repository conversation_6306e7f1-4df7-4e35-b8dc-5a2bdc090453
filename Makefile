# Espoir Sportive Chorbane - Makefile
# Commandes pour gérer l'application de gestion d'équipe de football

.PHONY: help setup dev prod stop restart clean logs status health test lint format build deploy backup restore

# Variables
COMPOSE_DEV = docker-compose -f docker-compose.dev.yml
COMPOSE_PROD = docker-compose -f docker-compose.prod.yml
BACKEND_CONTAINER = espoir-sportive-backend-dev
FRONTEND_CONTAINER = espoir-sportive-frontend-dev
DB_CONTAINER = espoir-sportive-postgres-dev

# Couleurs pour l'affichage
GREEN = \033[0;32m
YELLOW = \033[1;33m
RED = \033[0;31m
BLUE = \033[0;34m
PURPLE = \033[0;35m
NC = \033[0m # No Color

# Aide par défaut
help: ## Afficher cette aide
	@echo "$(GREEN)Espoir Sportive Chorbane - Commandes Make$(NC)"
	@echo "$(YELLOW)================================================$(NC)"
	@echo "$(BLUE)🏆 Application de gestion de club de football$(NC)"
	@echo "$(PURPLE)Design moderne inspiré du PSG avec React + Node.js$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "$(GREEN)%-25s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# =============================================================================
# COMMANDES DE DÉVELOPPEMENT
# =============================================================================

setup: ## Configuration initiale du projet
	@echo "$(GREEN)🔧 Configuration initiale...$(NC)"
	@cp backend/.env.example backend/.env 2>/dev/null || echo "Fichier .env déjà présent"
	@echo "$(GREEN)✅ Configuration terminée$(NC)"

dev: ## Démarrer l'environnement de développement
	@echo "$(GREEN)🚀 Démarrage de l'environnement de développement...$(NC)"
	$(COMPOSE_DEV) up -d
	@echo "$(GREEN)✅ Services démarrés$(NC)"
	@echo "$(YELLOW)🌐 Frontend React: http://localhost:3001$(NC)"
	@echo "$(YELLOW)🔧 Backend API: http://localhost:5001$(NC)"
	@echo "$(YELLOW)🗄️ Adminer: http://localhost:8081$(NC)"
	@echo "$(YELLOW)📧 MailHog: http://localhost:8025$(NC)"

dev-logs: ## Voir les logs en développement
	$(DOCKER_COMPOSE_DEV) logs -f

dev-stop: ## Arrêter les services de développement
	$(DOCKER_COMPOSE_DEV) down

# Production
prod: setup ## Démarrer en mode production
	@echo "$(GREEN)Démarrage en mode production...$(NC)"
	$(DOCKER_COMPOSE_PROD) up --build -d
	@echo "$(GREEN)Services démarrés !$(NC)"
	@echo "Application: http://localhost:3000"
	@echo "API: http://localhost:5000"

prod-logs: ## Voir les logs en production
	$(DOCKER_COMPOSE_PROD) logs -f

prod-stop: ## Arrêter les services de production
	$(DOCKER_COMPOSE_PROD) down

# Gestion générale
stop: ## Arrêter tous les services
	@echo "$(YELLOW)Arrêt de tous les services...$(NC)"
	-$(DOCKER_COMPOSE_DEV) down
	-$(DOCKER_COMPOSE_PROD) down
	@echo "$(GREEN)Services arrêtés$(NC)"

restart-dev: ## Redémarrer en développement
	$(DOCKER_COMPOSE_DEV) restart

restart-prod: ## Redémarrer en production
	$(DOCKER_COMPOSE_PROD) restart

# Logs
logs: ## Voir les logs (développement par défaut)
	$(DOCKER_COMPOSE_DEV) logs -f

logs-backend: ## Voir les logs du backend
	$(DOCKER_COMPOSE_DEV) logs -f backend-dev

logs-frontend: ## Voir les logs du frontend
	$(DOCKER_COMPOSE_DEV) logs -f frontend-dev

logs-db: ## Voir les logs de la base de données
	$(DOCKER_COMPOSE_DEV) logs -f postgres-dev

# Build et tests
build: ## Construire les images Docker
	@echo "$(GREEN)Construction des images...$(NC)"
	$(DOCKER_COMPOSE_DEV) build --no-cache
	$(DOCKER_COMPOSE_PROD) build --no-cache

test: ## Exécuter les tests
	@echo "$(GREEN)Exécution des tests...$(NC)"
	cd backend && npm test
	cd frontend && npm test

# Base de données
db-migrate: ## Exécuter les migrations de base de données
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:migrate

db-seed: ## Peupler la base de données avec des données de test basiques
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:seed

db-seed-advanced: ## Peupler avec des données Faker réalistes (recommandé)
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:seed-advanced

db-reset: ## Reset complet avec seed basique
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:reset

db-reset-advanced: ## Reset complet avec seed Faker (recommandé)
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:reset-advanced

db-generate: ## Générer le client Prisma
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate

db-studio: ## Ouvrir Prisma Studio
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:studio

db-backup: ## Sauvegarder la base de données
	@echo "$(GREEN)Sauvegarde de la base de données...$(NC)"
	docker-compose exec postgres-dev pg_dump -U espoir_dev espoir_sportive_dev > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "$(GREEN)Sauvegarde terminée$(NC)"

# Génération de données spécifiques avec Faker
db-generate-articles: ## Générer 10 articles avec Faker
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate-data articles 10

db-generate-matches: ## Générer 5 matchs avec Faker
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate-data matches 5

db-generate-supporters: ## Générer 20 supporters avec Faker
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate-data supporters 20

db-generate-comments: ## Générer des commentaires sur les articles
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate-data comments 5

db-generate-events: ## Générer des événements de match
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate-data events

db-generate-all: ## Générer un peu de tout avec Faker
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:generate-data all

db-download-images: ## Télécharger et intégrer les images du club
	@echo "$(GREEN)🖼️  Téléchargement des images d'Espoir Sportif de Chorbane...$(NC)"
	$(DOCKER_COMPOSE_DEV) exec backend-dev npm run db:download-images
	@echo "$(GREEN)✅ Images téléchargées et intégrées$(NC)"

# =============================================================================
# RACCOURCIS UTILES
# =============================================================================

quick-start: setup dev db-migrate db-seed-advanced db-download-images ## Démarrage rapide complet
	@echo "$(GREEN)🎉 Démarrage rapide terminé !$(NC)"
	@echo "$(BLUE)🏆 Espoir Sportif de Chorbane est prêt !$(NC)"
	@echo ""
	@echo "$(YELLOW)🌐 Frontend React: http://localhost:3001$(NC)"
	@echo "$(YELLOW)🔧 Backend API: http://localhost:5001$(NC)"
	@echo "$(YELLOW)🗄️ Adminer: http://localhost:8081$(NC)"
	@echo "$(YELLOW)📧 MailHog: http://localhost:8025$(NC)"
	@echo ""
	@echo "$(PURPLE)🔐 COMPTES DE DÉMONSTRATION :$(NC)"
	@echo "$(PURPLE)👨‍💼 Admin: <EMAIL> / admin123$(NC)"
	@echo "$(PURPLE)⚽ Joueur: <EMAIL> / player123$(NC)"
	@echo "$(PURPLE)🎉 Supporter: <EMAIL> / supporter123$(NC)"
	@echo ""
	@echo "$(GREEN)🎯 ACCÈS RAPIDE :$(NC)"
	@echo "$(GREEN)• Site principal: http://localhost:3001$(NC)"
	@echo "$(GREEN)• Connexion: http://localhost:3001/login$(NC)"
	@echo "$(GREEN)• Dashboard: http://localhost:3001/dashboard$(NC)"
	@echo "$(GREEN)• Réservations: http://localhost:3001/reservations$(NC)"

demo: quick-start ## Démo complète avec données Faker
	@echo "$(BLUE)🎭 Démo Espoir Sportif de Chorbane$(NC)"
	@echo "$(GREEN)✨ Design moderne avec couleurs vertes$(NC)"
	@echo "$(GREEN)📱 Interface responsive React$(NC)"
	@echo "$(GREEN)🗄️ 29 joueurs + 25 matchs + données réalistes$(NC)"
	@echo "$(GREEN)🖼️ Images intégrées + galerie complète$(NC)"
	@echo "$(GREEN)🔐 Système d'authentification JWT$(NC)"
	@echo "$(GREEN)🎫 Système de réservation de billets$(NC)"

# Accès aux conteneurs
shell-backend: ## Accéder au shell du backend
	$(DOCKER_COMPOSE_DEV) exec backend-dev sh

shell-frontend: ## Accéder au shell du frontend
	$(DOCKER_COMPOSE_DEV) exec frontend-dev sh

shell-db: ## Accéder au shell PostgreSQL
	$(DOCKER_COMPOSE_DEV) exec postgres-dev psql -U espoir_dev espoir_sportive_dev

# Nettoyage
clean: ## Nettoyage léger (conteneurs arrêtés)
	@echo "$(YELLOW)Nettoyage des conteneurs arrêtés...$(NC)"
	docker container prune -f
	docker image prune -f

clean-all: ## Nettoyage complet (ATTENTION: supprime tout)
	@echo "$(RED)ATTENTION: Cette commande va supprimer tous les volumes et données !$(NC)"
	@read -p "Êtes-vous sûr ? (y/N): " confirm && [ "$$confirm" = "y" ]
	$(DOCKER_COMPOSE_DEV) down -v
	$(DOCKER_COMPOSE_PROD) down -v
	docker system prune -a --volumes -f
	@echo "$(GREEN)Nettoyage complet terminé$(NC)"

clean-volumes: ## Supprimer les volumes (perte de données)
	@echo "$(RED)ATTENTION: Cette commande va supprimer toutes les données !$(NC)"
	@read -p "Êtes-vous sûr ? (y/N): " confirm && [ "$$confirm" = "y" ]
	docker volume rm $(PROJECT_NAME)_postgres_data $(PROJECT_NAME)_redis_data 2>/dev/null || true
	docker volume rm $(PROJECT_NAME)_postgres_dev_data $(PROJECT_NAME)_redis_dev_data 2>/dev/null || true

# Monitoring
status: ## Afficher le statut des services
	@echo "$(GREEN)Statut des services:$(NC)"
	@echo "\n$(YELLOW)Développement:$(NC)"
	-$(DOCKER_COMPOSE_DEV) ps
	@echo "\n$(YELLOW)Production:$(NC)"
	-$(DOCKER_COMPOSE_PROD) ps

stats: ## Afficher les statistiques des conteneurs
	docker stats --no-stream

health: ## Vérifier la santé des services
	@echo "$(GREEN)Vérification de la santé des services...$(NC)"
	@curl -f http://localhost:5001/api/health 2>/dev/null && echo "✅ Backend Dev OK" || echo "❌ Backend Dev KO"
	@curl -f http://localhost:3001 2>/dev/null && echo "✅ Frontend Dev OK" || echo "❌ Frontend Dev KO"
	@curl -f http://localhost:5000/api/health 2>/dev/null && echo "✅ Backend Prod OK" || echo "❌ Backend Prod KO"
	@curl -f http://localhost:3000 2>/dev/null && echo "✅ Frontend Prod OK" || echo "❌ Frontend Prod KO"

# Documentation (voir section dédiée plus bas)

# Docker utilitaires
docker-build: ## Construire les images Docker sans cache
	@echo "$(GREEN)Construction des images Docker...$(NC)"
	$(DOCKER_COMPOSE_DEV) build --no-cache
	$(DOCKER_COMPOSE_PROD) build --no-cache

docker-pull: ## Mettre à jour les images de base
	@echo "$(GREEN)Mise à jour des images de base...$(NC)"
	docker pull node:18-alpine
	docker pull postgres:15-alpine
	docker pull nginx:alpine
	docker pull redis:7-alpine

docker-prune: ## Nettoyer les images et conteneurs inutilisés
	@echo "$(YELLOW)Nettoyage Docker...$(NC)"
	docker system prune -f
	docker image prune -f

# Utilitaires
update: ## Mettre à jour les dépendances
	@echo "$(GREEN)Mise à jour des dépendances...$(NC)"
	cd backend && npm update
	cd frontend && npm update

lint: ## Vérifier la qualité du code
	@echo "$(GREEN)Vérification du code...$(NC)"
	cd backend && npm run lint || echo "Linting backend non configuré"
	cd frontend && npm run lint || echo "Linting frontend non configuré"

format: ## Formater le code
	@echo "$(GREEN)Formatage du code...$(NC)"
	cd backend && npm run format || echo "Formatage backend non configuré"
	cd frontend && npm run format || echo "Formatage frontend non configuré"

# Git helpers
commit: ## Faire un commit avec un message
	@read -p "Message du commit: " msg; git add . && git commit -m "$$msg"

push: ## Push vers le repository
	git push origin main

pull: ## Pull depuis le repository
	git pull origin main

# =============================================================================
# DOCUMENTATION
# =============================================================================

docs: ## Générer la documentation
	@echo "$(GREEN)📚 Documentation disponible:$(NC)"
	@echo "  • README.md - Documentation principale"
	@echo "  • CHANGELOG.md - Journal des modifications"
	@echo "  • docs/CONVERSATION_CHAT.md - Conversation de développement"
	@echo "  • docs/ - Documentation technique complète"

generate-pdf: ## Générer le PDF de conversation
	@echo "$(GREEN)📄 Génération du PDF de conversation...$(NC)"
	node scripts/generate-pdf.js
	@echo "$(YELLOW)💡 Ouvrez le fichier HTML dans votre navigateur et imprimez en PDF$(NC)"

info: ## Informations sur l'application
	@echo "$(BLUE)🏆 Espoir Sportif de Chorbane$(NC)"
	@echo "$(GREEN)Application de gestion de club de football$(NC)"
	@echo ""
	@echo "$(YELLOW)🎨 Frontend:$(NC) React 18 + TypeScript + Tailwind CSS"
	@echo "$(YELLOW)🔧 Backend:$(NC) Node.js + Express + Prisma + PostgreSQL"
	@echo "$(YELLOW)🐳 Infrastructure:$(NC) Docker + Docker Compose"
	@echo "$(YELLOW)🎭 Données:$(NC) Faker.js avec noms tunisiens"
	@echo "$(YELLOW)🎯 Design:$(NC) Couleurs vertes modernes"
	@echo ""
	@echo "$(PURPLE)📊 Fonctionnalités:$(NC)"
	@echo "  • Dashboard d'administration complet"
	@echo "  • Système d'authentification JWT"
	@echo "  • Gestion des joueurs et équipes"
	@echo "  • Calendrier des matchs et résultats"
	@echo "  • Système de réservation de billets"
	@echo "  • Actualités et blog"
	@echo "  • Statistiques avancées"
	@echo "  • Interface responsive moderne"
	@echo "  • Galerie d'images intégrée"

# Par défaut, afficher l'aide
.DEFAULT_GOAL := help
