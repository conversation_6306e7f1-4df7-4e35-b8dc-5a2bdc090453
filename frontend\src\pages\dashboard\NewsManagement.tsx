import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Calendar,
  User,
  Tag,
  Image,
  FileText
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface Article {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  category: string;
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    firstName: string;
    lastName: string;
  };
  featuredImage?: string;
  tags: string[];
  views: number;
}

const NewsManagement: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('ALL');
  const [categoryFilter, setCategoryFilter] = useState<string>('ALL');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);
  const [loading, setLoading] = useState(true);

  // Mock data - Replace with API call
  useEffect(() => {
    const mockArticles: Article[] = [
      {
        id: '1',
        title: 'Victoire éclatante contre le CA Bizertin',
        content: 'Lorem ipsum dolor sit amet...',
        excerpt: 'Une performance exceptionnelle de nos joueurs lors du dernier match.',
        category: 'MATCH',
        status: 'PUBLISHED',
        publishedAt: '2025-05-20T10:00:00',
        createdAt: '2025-05-20T09:00:00',
        updatedAt: '2025-05-20T10:00:00',
        author: {
          firstName: 'Admin',
          lastName: 'ESC'
        },
        featuredImage: '/images/match-victory.jpg',
        tags: ['match', 'victoire', 'championnat'],
        views: 1250
      },
      {
        id: '2',
        title: 'Nouveau joueur: Hamza Bedoui rejoint l\'équipe',
        content: 'Lorem ipsum dolor sit amet...',
        excerpt: 'Nous sommes fiers d\'annoncer l\'arrivée d\'un nouveau talent.',
        category: 'TRANSFERT',
        status: 'PUBLISHED',
        publishedAt: '2025-05-18T14:30:00',
        createdAt: '2025-05-18T13:00:00',
        updatedAt: '2025-05-18T14:30:00',
        author: {
          firstName: 'Admin',
          lastName: 'ESC'
        },
        featuredImage: '/images/new-player.jpg',
        tags: ['transfert', 'nouveau joueur'],
        views: 890
      },
      {
        id: '3',
        title: 'Préparation intensive pour la nouvelle saison',
        content: 'Lorem ipsum dolor sit amet...',
        excerpt: 'L\'équipe se prépare activement pour les défis à venir.',
        category: 'ENTRAINEMENT',
        status: 'DRAFT',
        createdAt: '2025-05-15T16:00:00',
        updatedAt: '2025-05-15T16:00:00',
        author: {
          firstName: 'Admin',
          lastName: 'ESC'
        },
        tags: ['entraînement', 'préparation'],
        views: 0
      }
    ];
    setArticles(mockArticles);
    setLoading(false);
  }, []);

  const categories = ['MATCH', 'TRANSFERT', 'ENTRAINEMENT', 'CLUB', 'COMMUNAUTE'];

  const filteredArticles = articles.filter(article => {
    const matchesSearch =
      article.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
      article.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'ALL' || article.status === statusFilter;
    const matchesCategory = categoryFilter === 'ALL' || article.category === categoryFilter;

    return matchesSearch && matchesStatus && matchesCategory;
  });

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      DRAFT: { color: 'bg-yellow-100 text-yellow-800', text: 'Brouillon' },
      PUBLISHED: { color: 'bg-green-100 text-green-800', text: 'Publié' },
      ARCHIVED: { color: 'bg-gray-100 text-gray-800', text: 'Archivé' }
    };

    const config = statusConfig[status as keyof typeof statusConfig];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const getCategoryBadge = (category: string) => {
    const categoryConfig = {
      MATCH: { color: 'bg-blue-100 text-blue-800', text: 'Match' },
      TRANSFERT: { color: 'bg-purple-100 text-purple-800', text: 'Transfert' },
      ENTRAINEMENT: { color: 'bg-orange-100 text-orange-800', text: 'Entraînement' },
      CLUB: { color: 'bg-green-100 text-green-800', text: 'Club' },
      COMMUNAUTE: { color: 'bg-pink-100 text-pink-800', text: 'Communauté' }
    };

    const config = categoryConfig[category as keyof typeof categoryConfig];
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  const handleDeleteArticle = (articleId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
      setArticles(articles.filter(article => article.id !== articleId));
    }
  };

  const handlePublishArticle = (articleId: string) => {
    setArticles(articles.map(article =>
      article.id === articleId
        ? { ...article, status: 'PUBLISHED' as const, publishedAt: new Date().toISOString() }
        : article
    ));
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center sm:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Gestion des actualités</h1>
            <p className="mt-1 text-sm text-gray-600">
              Gérez les articles et actualités d'Espoir Sportif de Chorbane
            </p>
          </div>
          <div className="mt-4 sm:mt-0">
            <button
              onClick={() => window.location.href = '/dashboard/news/add'}
              className="btn btn-primary"
            >
              <Plus className="h-5 w-5 mr-2" />
              Nouvel article
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white rounded-lg shadow p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Rechercher un article..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
            <div>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="input"
              >
                <option value="ALL">Tous les statuts</option>
                <option value="DRAFT">Brouillons</option>
                <option value="PUBLISHED">Publiés</option>
                <option value="ARCHIVED">Archivés</option>
              </select>
            </div>
            <div>
              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="input"
              >
                <option value="ALL">Toutes les catégories</option>
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
            </div>
            <div className="text-sm text-gray-600 flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              {filteredArticles.length} article{filteredArticles.length > 1 ? 's' : ''} trouvé{filteredArticles.length > 1 ? 's' : ''}
            </div>
          </div>
        </div>

        {/* Articles List */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Article
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Catégorie
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Statut
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Auteur
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Vues
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredArticles.map((article) => (
                  <tr key={article.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4">
                      <div className="flex items-center">
                        {article.featuredImage && (
                          <div className="flex-shrink-0 h-12 w-12 mr-4">
                            <img
                              className="h-12 w-12 rounded-lg object-cover"
                              src={article.featuredImage}
                              alt=""
                            />
                          </div>
                        )}
                        <div>
                          <div className="text-sm font-medium text-gray-900 line-clamp-1">
                            {article.title}
                          </div>
                          <div className="text-sm text-gray-500 line-clamp-2">
                            {article.excerpt}
                          </div>
                          {article.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-1">
                              {article.tags.slice(0, 3).map((tag, index) => (
                                <span key={index} className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                  <Tag className="h-3 w-3 mr-1" />
                                  {tag}
                                </span>
                              ))}
                              {article.tags.length > 3 && (
                                <span className="text-xs text-gray-500">+{article.tags.length - 3}</span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getCategoryBadge(article.category)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {getStatusBadge(article.status)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-400" />
                        <div className="text-sm text-gray-900">
                          {article.author.firstName} {article.author.lastName}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {article.publishedAt ? formatDate(article.publishedAt) : formatDate(article.createdAt)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {article.publishedAt ? 'Publié' : 'Créé'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <Eye className="h-4 w-4 mr-1 text-gray-400" />
                        {article.views.toLocaleString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {article.status === 'DRAFT' && (
                          <button
                            onClick={() => handlePublishArticle(article.id)}
                            className="text-blue-600 hover:text-blue-900 text-xs bg-blue-100 px-2 py-1 rounded"
                          >
                            Publier
                          </button>
                        )}
                        <button
                          onClick={() => setEditingArticle(article)}
                          className="text-green-600 hover:text-green-900"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteArticle(article.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredArticles.length === 0 && (
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Aucun article trouvé</h3>
            <p className="mt-1 text-sm text-gray-500">
              Commencez par créer un nouvel article.
            </p>
            <div className="mt-6">
              <button
                onClick={() => setShowAddModal(true)}
                className="btn btn-primary"
              >
                <Plus className="h-5 w-5 mr-2" />
                Nouvel article
              </button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default NewsManagement;
