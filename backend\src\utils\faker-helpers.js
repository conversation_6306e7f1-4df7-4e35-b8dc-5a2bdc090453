const { faker } = require('@faker-js/faker');

// Configuration Faker en français
faker.locale = 'fr';

// Données spécifiques au football tunisien
const TUNISIAN_DATA = {
  firstNames: [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>'
  ],
  
  lastNames: [
    '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>douni', 'Talbi', 'Meriah', 'Bronn', 'Mathlouthi', 'Haddadi'
  ],
  
  cities: [
    'Tunis', 'Sfax', 'Sousse', 'Kairouan', 'Bizerte', 'Gabès', 'Ariana',
    'Gafsa', 'Monastir', 'Ben Arous', 'Kasserine', 'Médenine', 'Nabeul',
    'Tataouine', 'Béja', 'Jendouba', 'Mahdia', 'Siliana', 'Manouba', 'Zaghouan',
    'Kef', 'Sidi Bouzid', 'Tozeur', 'Kebili'
  ],
  
  footballClubs: [
    'AS Chorbane', 'ES Tunis', 'CA Bizertin', 'US Monastir', 'CS Sfaxien',
    'Club Africain', 'Stade Tunisien', 'AS Soliman', 'US Ben Guerdane',
    'ES Métlaoui', 'CS Hammam-Lif', 'AS Marsa', 'US Tataouine', 'ES Zarzis',
    'CA Redeyef', 'AS Kasserine', 'US Siliana', 'ES Jerba'
  ],
  
  stadiums: [
    'Stade Municipal Chorbane', 'Stade Olympique de Radès', 'Stade Taïeb Mhiri',
    'Stade Mustapha Ben Jannet', 'Stade Hédi Enneifer', 'Stade 15 Octobre',
    'Stade Chedli Zouiten', 'Stade Zouhaier Essaafi', 'Stade Municipal de Bizerte',
    'Stade Mustapha Zitouna', 'Stade Bou Ali Lahouar'
  ]
};

// Fonctions utilitaires
const getRandomElement = (array) => array[Math.floor(Math.random() * array.length)];

/**
 * Génère un nom tunisien réaliste
 * @returns {Object} {firstName, lastName}
 */
const generateTunisianName = () => ({
  firstName: getRandomElement(TUNISIAN_DATA.firstNames),
  lastName: getRandomElement(TUNISIAN_DATA.lastNames)
});

/**
 * Génère un email réaliste basé sur le nom
 * @param {string} firstName 
 * @param {string} lastName 
 * @returns {string} Email généré
 */
const generateRealisticEmail = (firstName, lastName) => {
  const domains = ['gmail.com', 'yahoo.fr', 'hotmail.com', 'outlook.com', 'live.com', 'orange.tn', 'topnet.tn'];
  const cleanFirstName = firstName.toLowerCase().replace(/\s+/g, '').replace(/[àáâãäå]/g, 'a').replace(/[èéêë]/g, 'e');
  const cleanLastName = lastName.toLowerCase().replace(/\s+/g, '').replace(/[àáâãäå]/g, 'a').replace(/[èéêë]/g, 'e');
  const domain = getRandomElement(domains);
  
  const emailFormats = [
    `${cleanFirstName}.${cleanLastName}@${domain}`,
    `${cleanFirstName}${cleanLastName}@${domain}`,
    `${cleanFirstName}_${cleanLastName}@${domain}`,
    `${cleanFirstName}${Math.floor(Math.random() * 100)}@${domain}`,
    `${cleanFirstName.charAt(0)}${cleanLastName}@${domain}`
  ];
  
  return getRandomElement(emailFormats);
};

/**
 * Génère un numéro de téléphone tunisien réaliste
 * @returns {string} Numéro de téléphone formaté
 */
const generateTunisianPhone = () => {
  const mobilePrefixes = ['20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '50', '51', '52', '53', '54', '55', '56', '57', '58', '59'];
  const prefix = getRandomElement(mobilePrefixes);
  const number = faker.string.numeric(6);
  return `+216 ${prefix} ${number.substring(0, 3)} ${number.substring(3)}`;
};

/**
 * Génère des caractéristiques physiques réalistes selon la position
 * @param {string} position - Position du joueur
 * @returns {Object} {height, weight}
 */
const generatePlayerPhysicalStats = (position) => {
  switch (position) {
    case 'GOALKEEPER':
      return {
        height: faker.number.float({ min: 1.80, max: 1.95, precision: 0.01 }),
        weight: faker.number.float({ min: 75, max: 90, precision: 0.1 })
      };
    case 'DEFENDER':
      return {
        height: faker.number.float({ min: 1.75, max: 1.90, precision: 0.01 }),
        weight: faker.number.float({ min: 70, max: 85, precision: 0.1 })
      };
    case 'MIDFIELDER':
      return {
        height: faker.number.float({ min: 1.65, max: 1.80, precision: 0.01 }),
        weight: faker.number.float({ min: 65, max: 80, precision: 0.1 })
      };
    case 'FORWARD':
      return {
        height: faker.number.float({ min: 1.70, max: 1.85, precision: 0.01 }),
        weight: faker.number.float({ min: 68, max: 82, precision: 0.1 })
      };
    default:
      return {
        height: faker.number.float({ min: 1.65, max: 1.90, precision: 0.01 }),
        weight: faker.number.float({ min: 65, max: 85, precision: 0.1 })
      };
  }
};

/**
 * Génère des statistiques réalistes selon la position
 * @param {string} position - Position du joueur
 * @param {number} matchesPlayed - Nombre de matchs joués
 * @returns {Object} {goals, assists, yellowCards, redCards, minutesPlayed}
 */
const generatePlayerGameStats = (position, matchesPlayed = null) => {
  const matches = matchesPlayed || faker.number.int({ min: 5, max: 20 });
  
  let goals, assists;
  
  switch (position) {
    case 'GOALKEEPER':
      goals = faker.number.int({ min: 0, max: 1 });
      assists = faker.number.int({ min: 0, max: 2 });
      break;
    case 'DEFENDER':
      goals = faker.number.int({ min: 0, max: 4 });
      assists = faker.number.int({ min: 0, max: 6 });
      break;
    case 'MIDFIELDER':
      goals = faker.number.int({ min: 1, max: 8 });
      assists = faker.number.int({ min: 2, max: 12 });
      break;
    case 'FORWARD':
      goals = faker.number.int({ min: 3, max: 15 });
      assists = faker.number.int({ min: 1, max: 8 });
      break;
    default:
      goals = faker.number.int({ min: 0, max: 5 });
      assists = faker.number.int({ min: 0, max: 5 });
  }
  
  return {
    matchesPlayed: matches,
    goals,
    assists,
    yellowCards: faker.number.int({ min: 0, max: 5 }),
    redCards: faker.number.int({ min: 0, max: 2 }),
    minutesPlayed: matches * faker.number.int({ min: 45, max: 90 })
  };
};

/**
 * Génère un titre d'article de football réaliste
 * @param {string} type - Type d'article
 * @param {Object} context - Contexte (équipe, joueur, etc.)
 * @returns {string} Titre généré
 */
const generateFootballArticleTitle = (type, context = {}) => {
  const templates = {
    'match_result': [
      'Victoire éclatante contre {team} ({score})',
      'Match nul héroïque face à {team}',
      'Défaite courageuse contre {team}',
      'Remontada spectaculaire contre {team}',
      'Derby passionnant contre {team}'
    ],
    'recruitment': [
      'Bienvenue à {player} !',
      'Nouveau renfort : {player} rejoint l\'équipe',
      'Recrutement : {player} signe à Espoir Sportive',
      'Transfert : {player} nous rejoint',
      'Officiel : {player} est un joueur d\'Espoir Sportive'
    ],
    'training': [
      'Préparation intensive pour la nouvelle saison',
      'Stage de préparation réussi',
      'L\'équipe se prépare pour les défis à venir',
      'Reprise des entraînements',
      'Stage de cohésion à {location}'
    ],
    'interview': [
      'Interview exclusive avec {player}',
      '{player} se confie sur ses ambitions',
      'Rencontre avec {player}, pilier de l\'équipe',
      '{player} : "Nous visons le titre cette saison"'
    ],
    'general': [
      'Assemblée générale du club',
      'Nouveaux équipements pour l\'équipe',
      'Partenariat avec {sponsor}',
      'Travaux d\'amélioration du stade',
      'Bilan de mi-saison positif'
    ]
  };
  
  const typeTemplates = templates[type] || templates['general'];
  let title = getRandomElement(typeTemplates);
  
  // Remplacer les placeholders
  if (title.includes('{team}') && context.team) {
    title = title.replace('{team}', context.team);
  } else if (title.includes('{team}')) {
    title = title.replace('{team}', getRandomElement(TUNISIAN_DATA.footballClubs));
  }
  
  if (title.includes('{player}') && context.player) {
    title = title.replace('{player}', context.player);
  }
  
  if (title.includes('{score}') && context.score) {
    title = title.replace('{score}', context.score);
  }
  
  if (title.includes('{location}') && context.location) {
    title = title.replace('{location}', context.location);
  } else if (title.includes('{location}')) {
    title = title.replace('{location}', getRandomElement(TUNISIAN_DATA.cities));
  }
  
  if (title.includes('{sponsor}')) {
    title = title.replace('{sponsor}', faker.company.name());
  }
  
  return title;
};

/**
 * Génère un score de match réaliste
 * @returns {Object} {homeScore, awayScore}
 */
const generateRealisticMatchScore = () => {
  // Distribution réaliste des scores de football
  const scoreDistribution = [
    { home: 0, away: 0, weight: 8 },  // 0-0
    { home: 1, away: 0, weight: 15 }, // 1-0
    { home: 0, away: 1, weight: 15 }, // 0-1
    { home: 1, away: 1, weight: 12 }, // 1-1
    { home: 2, away: 0, weight: 10 }, // 2-0
    { home: 0, away: 2, weight: 10 }, // 0-2
    { home: 2, away: 1, weight: 8 },  // 2-1
    { home: 1, away: 2, weight: 8 },  // 1-2
    { home: 3, away: 0, weight: 5 },  // 3-0
    { home: 0, away: 3, weight: 5 },  // 0-3
    { home: 2, away: 2, weight: 4 },  // 2-2
  ];
  
  // Ajouter quelques scores plus élevés avec moins de probabilité
  for (let i = 3; i <= 5; i++) {
    for (let j = 1; j <= 3; j++) {
      scoreDistribution.push({ home: i, away: j, weight: 1 });
      scoreDistribution.push({ home: j, away: i, weight: 1 });
    }
  }
  
  const totalWeight = scoreDistribution.reduce((sum, score) => sum + score.weight, 0);
  const random = Math.random() * totalWeight;
  
  let currentWeight = 0;
  for (const score of scoreDistribution) {
    currentWeight += score.weight;
    if (random <= currentWeight) {
      return { homeScore: score.home, awayScore: score.away };
    }
  }
  
  // Fallback
  return { homeScore: 1, awayScore: 1 };
};

module.exports = {
  TUNISIAN_DATA,
  getRandomElement,
  generateTunisianName,
  generateRealisticEmail,
  generateTunisianPhone,
  generatePlayerPhysicalStats,
  generatePlayerGameStats,
  generateFootballArticleTitle,
  generateRealisticMatchScore,
  faker
};
