# Script PowerShell de démarrage Docker pour Espoir Sportive Chorbane
# Usage: .\scripts\docker-start.ps1 [dev|prod|stop|clean|logs|help]

param(
    [Parameter(Position=0)]
    [ValidateSet("dev", "prod", "stop", "clean", "logs", "help")]
    [string]$Command = "help",
    
    [Parameter(Position=1)]
    [string]$Environment = "",
    
    [Parameter(Position=2)]
    [string]$Service = ""
)

# Fonction pour afficher les messages colorés
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Log-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Blue"
}

function Log-Success {
    param([string]$Message)
    Write-ColorOutput "[SUCCESS] $Message" "Green"
}

function Log-Warning {
    param([string]$Message)
    Write-ColorOutput "[WARNING] $Message" "Yellow"
}

function Log-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

# Vérifier que Docker est installé
function Test-Docker {
    try {
        $dockerVersion = docker --version 2>$null
        if (-not $dockerVersion) {
            throw "Docker non trouvé"
        }
        
        $composeVersion = docker-compose --version 2>$null
        if (-not $composeVersion) {
            throw "Docker Compose non trouvé"
        }
        
        Log-Info "Docker et Docker Compose sont installés"
        return $true
    }
    catch {
        Log-Error "Docker ou Docker Compose n'est pas installé. Veuillez les installer d'abord."
        return $false
    }
}

# Créer les dossiers nécessaires
function New-Directories {
    Log-Info "Création des dossiers nécessaires..."
    
    $directories = @(
        "data\postgres",
        "data\redis", 
        "data\uploads",
        "data\logs",
        "nginx\ssl"
    )
    
    foreach ($dir in $directories) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
        }
    }
    
    Log-Success "Dossiers créés avec succès"
}

# Configurer le fichier d'environnement
function Set-Environment {
    if (-not (Test-Path ".env")) {
        Log-Info "Copie du fichier d'environnement..."
        Copy-Item ".env.docker" ".env"
        Log-Warning "Fichier .env créé. Veuillez le modifier selon vos besoins."
    } else {
        Log-Info "Fichier .env existe déjà"
    }
}

# Démarrage en mode développement
function Start-Development {
    Log-Info "Démarrage en mode développement..."
    
    New-Directories
    Set-Environment
    
    Log-Info "Construction et démarrage des conteneurs de développement..."
    docker-compose -f docker-compose.dev.yml up --build -d
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "Environnement de développement démarré !"
        Log-Info "Services disponibles :"
        Write-Host "  - Frontend: http://localhost:3001" -ForegroundColor Cyan
        Write-Host "  - Backend API: http://localhost:5001" -ForegroundColor Cyan
        Write-Host "  - Adminer (DB): http://localhost:8081" -ForegroundColor Cyan
        Write-Host "  - MailHog: http://localhost:8025" -ForegroundColor Cyan
        Write-Host "  - Redis: localhost:6380" -ForegroundColor Cyan
        
        Log-Info "Pour voir les logs : docker-compose -f docker-compose.dev.yml logs -f"
    } else {
        Log-Error "Erreur lors du démarrage des conteneurs"
    }
}

# Démarrage en mode production
function Start-Production {
    Log-Info "Démarrage en mode production..."
    
    New-Directories
    Set-Environment
    
    Log-Info "Construction et démarrage des conteneurs de production..."
    docker-compose up --build -d
    
    if ($LASTEXITCODE -eq 0) {
        Log-Success "Environnement de production démarré !"
        Log-Info "Services disponibles :"
        Write-Host "  - Application: http://localhost:3000" -ForegroundColor Cyan
        Write-Host "  - API: http://localhost:5000" -ForegroundColor Cyan
        Write-Host "  - Nginx: http://localhost:80" -ForegroundColor Cyan
        
        Log-Info "Pour voir les logs : docker-compose logs -f"
    } else {
        Log-Error "Erreur lors du démarrage des conteneurs"
    }
}

# Arrêt des services
function Stop-Services {
    Log-Info "Arrêt des services..."
    
    if (Test-Path "docker-compose.dev.yml") {
        docker-compose -f docker-compose.dev.yml down
    }
    
    if (Test-Path "docker-compose.yml") {
        docker-compose down
    }
    
    Log-Success "Services arrêtés"
}

# Nettoyage complet
function Clear-All {
    Log-Warning "Nettoyage complet (suppression des volumes et images)..."
    $confirmation = Read-Host "Êtes-vous sûr ? Cette action est irréversible. (y/N)"
    
    if ($confirmation -eq "y" -or $confirmation -eq "Y") {
        Stop-Services
        
        Log-Info "Suppression des volumes..."
        docker volume prune -f
        
        Log-Info "Suppression des images..."
        docker image prune -a -f
        
        Log-Info "Suppression des réseaux..."
        docker network prune -f
        
        Log-Success "Nettoyage terminé"
    } else {
        Log-Info "Nettoyage annulé"
    }
}

# Affichage des logs
function Show-Logs {
    param(
        [string]$Env,
        [string]$ServiceName
    )
    
    if ($Env -eq "dev") {
        if ($ServiceName) {
            docker-compose -f docker-compose.dev.yml logs -f $ServiceName
        } else {
            docker-compose -f docker-compose.dev.yml logs -f
        }
    } else {
        if ($ServiceName) {
            docker-compose logs -f $ServiceName
        } else {
            docker-compose logs -f
        }
    }
}

# Affichage de l'aide
function Show-Help {
    Write-Host "Usage: .\scripts\docker-start.ps1 [COMMAND] [OPTIONS]" -ForegroundColor White
    Write-Host ""
    Write-Host "Commandes disponibles :" -ForegroundColor Yellow
    Write-Host "  dev         Démarrer en mode développement" -ForegroundColor Green
    Write-Host "  prod        Démarrer en mode production" -ForegroundColor Green
    Write-Host "  stop        Arrêter tous les services" -ForegroundColor Green
    Write-Host "  clean       Nettoyage complet (volumes, images, réseaux)" -ForegroundColor Green
    Write-Host "  logs [env]  Afficher les logs (dev ou prod)" -ForegroundColor Green
    Write-Host "  help        Afficher cette aide" -ForegroundColor Green
    Write-Host ""
    Write-Host "Exemples :" -ForegroundColor Yellow
    Write-Host "  .\scripts\docker-start.ps1 dev                    # Démarrer en développement" -ForegroundColor Cyan
    Write-Host "  .\scripts\docker-start.ps1 prod                   # Démarrer en production" -ForegroundColor Cyan
    Write-Host "  .\scripts\docker-start.ps1 logs dev               # Logs de développement" -ForegroundColor Cyan
    Write-Host "  .\scripts\docker-start.ps1 logs prod backend      # Logs du backend en production" -ForegroundColor Cyan
}

# Script principal
function Main {
    if (-not (Test-Docker)) {
        exit 1
    }
    
    switch ($Command) {
        "dev" {
            Start-Development
        }
        "prod" {
            Start-Production
        }
        "stop" {
            Stop-Services
        }
        "clean" {
            Clear-All
        }
        "logs" {
            Show-Logs $Environment $Service
        }
        default {
            Show-Help
        }
    }
}

# Exécution du script
Main
