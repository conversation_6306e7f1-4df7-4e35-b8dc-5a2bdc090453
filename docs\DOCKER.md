# Guide Docker - Espoir Sportive Chorbane

## 📋 Vue d'ensemble

Ce guide explique comment utiliser Docker et Docker Compose pour déployer l'application Espoir Sportive Chorbane en développement et en production.

## 🏗️ Architecture Docker

### Services Disponibles

#### Production (`docker-compose.yml`)
- **postgres** : Base de données PostgreSQL 15
- **backend** : API Node.js/Express
- **frontend** : Application React servie par Nginx
- **nginx** : Reverse proxy (optionnel, profil production)
- **redis** : Cache Redis (optionnel, profil cache)
- **adminer** : Interface de gestion DB (profil development)

#### Développement (`docker-compose.dev.yml`)
- **postgres-dev** : Base de données PostgreSQL pour dev
- **backend-dev** : API avec hot reload
- **frontend-dev** : React avec hot reload
- **adminer-dev** : Interface de gestion DB
- **redis-dev** : Cache Redis pour dev
- **mailhog** : Serveur email de test

## 🚀 Démarrage Rapide

### Prérequis
- Docker v20.0.0+
- Docker Compose v2.0.0+

### Scripts de Démarrage

#### Linux/macOS
```bash
# Rendre le script exécutable
chmod +x scripts/docker-start.sh

# Démarrer en développement
./scripts/docker-start.sh dev

# Démarrer en production
./scripts/docker-start.sh prod

# Arrêter tous les services
./scripts/docker-start.sh stop

# Voir l'aide
./scripts/docker-start.sh help
```

#### Windows (PowerShell)
```powershell
# Démarrer en développement
.\scripts\docker-start.ps1 dev

# Démarrer en production
.\scripts\docker-start.ps1 prod

# Arrêter tous les services
.\scripts\docker-start.ps1 stop

# Voir l'aide
.\scripts\docker-start.ps1 help
```

### Démarrage Manuel

#### Développement
```bash
# Créer les dossiers nécessaires
mkdir -p data/{postgres,redis,uploads,logs}

# Copier le fichier d'environnement
cp .env.docker .env

# Démarrer les services
docker-compose -f docker-compose.dev.yml up --build -d

# Voir les logs
docker-compose -f docker-compose.dev.yml logs -f
```

#### Production
```bash
# Créer les dossiers nécessaires
mkdir -p data/{postgres,redis,uploads,logs}

# Copier le fichier d'environnement
cp .env.docker .env

# Démarrer les services
docker-compose up --build -d

# Voir les logs
docker-compose logs -f
```

## 🔧 Configuration

### Variables d'Environnement

Le fichier `.env.docker` contient toutes les variables nécessaires :

```env
# Base de données
POSTGRES_DB=espoir_sportive_db
POSTGRES_USER=espoir_user
POSTGRES_PASSWORD=espoir_password_secure_2024

# Backend
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
NODE_ENV=production

# Frontend
VITE_API_URL=http://localhost:5000/api
VITE_APP_NAME=Espoir Sportive Chorbane

# Ports
BACKEND_PORT=5000
FRONTEND_PORT=3000
NGINX_HTTP_PORT=80
```

### Personnalisation

#### Modifier les Ports
```yaml
# Dans docker-compose.yml
services:
  backend:
    ports:
      - "5001:5000"  # Port externe:interne
  
  frontend:
    ports:
      - "3001:80"
```

#### Ajouter des Volumes Persistants
```yaml
services:
  backend:
    volumes:
      - ./custom-uploads:/app/uploads
      - ./custom-logs:/app/logs
```

## 📊 Services et Ports

### Développement
| Service | Port | URL | Description |
|---------|------|-----|-------------|
| Frontend | 3001 | http://localhost:3001 | Application React |
| Backend | 5001 | http://localhost:5001 | API Node.js |
| PostgreSQL | 5433 | localhost:5433 | Base de données |
| Adminer | 8081 | http://localhost:8081 | Interface DB |
| Redis | 6380 | localhost:6380 | Cache |
| MailHog | 8025 | http://localhost:8025 | Interface email |

### Production
| Service | Port | URL | Description |
|---------|------|-----|-------------|
| Frontend | 3000 | http://localhost:3000 | Application React |
| Backend | 5000 | http://localhost:5000 | API Node.js |
| PostgreSQL | 5432 | localhost:5432 | Base de données |
| Nginx | 80/443 | http://localhost | Reverse proxy |

## 🗄️ Gestion des Données

### Volumes Persistants
```bash
# Lister les volumes
docker volume ls

# Inspecter un volume
docker volume inspect espoir-sportive-chorbane_postgres_data

# Sauvegarder un volume
docker run --rm -v espoir-sportive-chorbane_postgres_data:/data -v $(pwd):/backup alpine tar czf /backup/postgres_backup.tar.gz /data
```

### Sauvegarde de la Base de Données
```bash
# Sauvegarde
docker-compose exec postgres pg_dump -U espoir_user espoir_sportive_db > backup.sql

# Restauration
docker-compose exec -T postgres psql -U espoir_user espoir_sportive_db < backup.sql
```

### Migration des Données
```bash
# Exécuter les migrations Prisma
docker-compose exec backend npm run db:migrate

# Seed de la base de données
docker-compose exec backend npm run db:seed

# Reset complet
docker-compose exec backend npm run db:reset
```

## 🔍 Debugging et Logs

### Voir les Logs
```bash
# Tous les services
docker-compose logs -f

# Service spécifique
docker-compose logs -f backend

# Dernières 100 lignes
docker-compose logs --tail=100 backend

# Logs depuis une heure
docker-compose logs --since=1h backend
```

### Accéder aux Conteneurs
```bash
# Shell dans le conteneur backend
docker-compose exec backend sh

# Shell dans le conteneur frontend
docker-compose exec frontend sh

# Shell dans PostgreSQL
docker-compose exec postgres psql -U espoir_user espoir_sportive_db
```

### Debugging du Backend
```bash
# Voir les variables d'environnement
docker-compose exec backend env

# Vérifier la santé du service
docker-compose exec backend curl http://localhost:5000/api/health

# Redémarrer un service
docker-compose restart backend
```

## 🔧 Maintenance

### Mise à Jour des Images
```bash
# Reconstruire les images
docker-compose build --no-cache

# Mettre à jour et redémarrer
docker-compose up --build -d
```

### Nettoyage
```bash
# Arrêter et supprimer les conteneurs
docker-compose down

# Supprimer les volumes (ATTENTION: perte de données)
docker-compose down -v

# Nettoyer les images inutilisées
docker image prune -a

# Nettoyage complet du système Docker
docker system prune -a --volumes
```

### Monitoring des Ressources
```bash
# Utilisation des ressources
docker stats

# Espace disque utilisé par Docker
docker system df

# Informations sur les conteneurs
docker-compose ps
```

## 🚀 Déploiement Production

### Préparation
1. **Configurer les variables d'environnement** :
   ```bash
   cp .env.docker .env
   # Modifier .env avec les vraies valeurs de production
   ```

2. **Configurer SSL** (si utilisation de Nginx) :
   ```bash
   # Placer les certificats SSL
   cp your-cert.pem nginx/ssl/cert.pem
   cp your-key.pem nginx/ssl/key.pem
   ```

3. **Optimiser pour la production** :
   ```yaml
   # Dans docker-compose.yml
   services:
     backend:
       environment:
         NODE_ENV: production
       restart: unless-stopped
   ```

### Déploiement avec Nginx
```bash
# Démarrer avec le profil production
docker-compose --profile production up -d

# Vérifier que Nginx fonctionne
curl -I http://localhost
```

### Monitoring en Production
```bash
# Vérifier la santé des services
docker-compose exec backend curl http://localhost:5000/api/health
docker-compose exec frontend curl http://localhost:80/health

# Surveiller les logs en continu
docker-compose logs -f --tail=50
```

## 🔒 Sécurité

### Bonnes Pratiques
1. **Changer les mots de passe par défaut** dans `.env`
2. **Utiliser des secrets Docker** pour les données sensibles
3. **Limiter l'exposition des ports** (ne pas exposer PostgreSQL en production)
4. **Utiliser HTTPS** en production avec des certificats valides
5. **Mettre à jour régulièrement** les images de base

### Configuration Sécurisée
```yaml
# Exemple de configuration sécurisée
services:
  postgres:
    # Ne pas exposer le port en production
    # ports:
    #   - "5432:5432"
    environment:
      POSTGRES_PASSWORD_FILE: /run/secrets/postgres_password
    secrets:
      - postgres_password

secrets:
  postgres_password:
    file: ./secrets/postgres_password.txt
```

## 🆘 Résolution de Problèmes

### Problèmes Courants

#### "Port already in use"
```bash
# Trouver le processus utilisant le port
netstat -tulpn | grep :5000
# ou
lsof -i :5000

# Changer le port dans docker-compose.yml
ports:
  - "5001:5000"
```

#### "Cannot connect to database"
```bash
# Vérifier que PostgreSQL est démarré
docker-compose ps postgres

# Vérifier les logs PostgreSQL
docker-compose logs postgres

# Tester la connexion
docker-compose exec postgres psql -U espoir_user espoir_sportive_db
```

#### "Build failed"
```bash
# Nettoyer le cache Docker
docker builder prune

# Reconstruire sans cache
docker-compose build --no-cache

# Vérifier l'espace disque
docker system df
```

#### "Service unhealthy"
```bash
# Vérifier les health checks
docker-compose ps

# Voir les logs détaillés
docker-compose logs service-name

# Redémarrer le service
docker-compose restart service-name
```

### Commandes de Diagnostic
```bash
# État des services
docker-compose ps

# Utilisation des ressources
docker stats

# Informations réseau
docker network ls
docker network inspect espoir-sportive-chorbane_espoir-network

# Informations sur les volumes
docker volume ls
docker volume inspect volume-name
```

---

*Documentation Docker mise à jour le 24/05/2025*
