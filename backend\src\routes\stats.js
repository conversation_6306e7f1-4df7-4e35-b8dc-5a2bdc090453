const express = require('express');
const { prisma } = require('../config/database');
const { authenticateToken, requirePlayerOrAdmin } = require('../middleware/auth');

const router = express.Router();

// Middleware d'authentification pour toutes les routes
router.use(authenticateToken);

// GET /api/stats/team - Statistiques globales de l'équipe
router.get('/team', requirePlayerOrAdmin, async (req, res) => {
  try {
    const { season = '2024-2025' } = req.query;

    // Statistiques des matchs
    const matchStats = await prisma.match.groupBy({
      by: ['status'],
      _count: {
        status: true
      },
      where: {
        matchDate: {
          gte: new Date(`${season.split('-')[0]}-07-01`),
          lt: new Date(`${season.split('-')[1]}-07-01`)
        }
      }
    });

    // Résultats des matchs terminés
    const finishedMatches = await prisma.match.findMany({
      where: {
        status: 'FINISHED',
        matchDate: {
          gte: new Date(`${season.split('-')[0]}-07-01`),
          lt: new Date(`${season.split('-')[1]}-07-01`)
        }
      },
      select: {
        homeScore: true,
        awayScore: true,
        isHome: true
      }
    });

    // Calculer victoires, défaites, nuls
    let wins = 0, draws = 0, losses = 0;
    let goalsFor = 0, goalsAgainst = 0;

    finishedMatches.forEach(match => {
      const ourScore = match.isHome ? match.homeScore : match.awayScore;
      const opponentScore = match.isHome ? match.awayScore : match.homeScore;
      
      goalsFor += ourScore;
      goalsAgainst += opponentScore;
      
      if (ourScore > opponentScore) wins++;
      else if (ourScore < opponentScore) losses++;
      else draws++;
    });

    // Statistiques des joueurs
    const playerStats = await prisma.playerStats.aggregate({
      where: { season },
      _sum: {
        goals: true,
        assists: true,
        yellowCards: true,
        redCards: true,
        matchesPlayed: true,
        minutesPlayed: true
      }
    });

    // Meilleurs buteurs
    const topScorers = await prisma.playerStats.findMany({
      where: { 
        season,
        goals: { gt: 0 }
      },
      include: {
        player: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: {
        goals: 'desc'
      },
      take: 5
    });

    // Meilleurs passeurs
    const topAssisters = await prisma.playerStats.findMany({
      where: { 
        season,
        assists: { gt: 0 }
      },
      include: {
        player: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: {
        assists: 'desc'
      },
      take: 5
    });

    // Joueurs les plus présents
    const mostActivePlayer = await prisma.playerStats.findMany({
      where: { 
        season,
        matchesPlayed: { gt: 0 }
      },
      include: {
        player: {
          include: {
            user: {
              select: {
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          }
        }
      },
      orderBy: {
        matchesPlayed: 'desc'
      },
      take: 5
    });

    // Statistiques des entraînements
    const trainingStats = await prisma.training.count({
      where: {
        date: {
          gte: new Date(`${season.split('-')[0]}-07-01`),
          lt: new Date(`${season.split('-')[1]}-07-01`)
        }
      }
    });

    // Taux de présence moyen aux entraînements
    const attendanceStats = await prisma.trainingAttendance.groupBy({
      by: ['isPresent'],
      _count: {
        isPresent: true
      },
      where: {
        training: {
          date: {
            gte: new Date(`${season.split('-')[0]}-07-01`),
            lt: new Date(`${season.split('-')[1]}-07-01`)
          }
        }
      }
    });

    const totalAttendances = attendanceStats.reduce((sum, stat) => sum + stat._count.isPresent, 0);
    const presentAttendances = attendanceStats.find(stat => stat.isPresent)?._count.isPresent || 0;
    const attendanceRate = totalAttendances > 0 ? (presentAttendances / totalAttendances) * 100 : 0;

    res.json({
      message: 'Statistiques de l\'équipe récupérées avec succès',
      stats: {
        season,
        matches: {
          total: finishedMatches.length,
          wins,
          draws,
          losses,
          winRate: finishedMatches.length > 0 ? (wins / finishedMatches.length) * 100 : 0,
          goalsFor,
          goalsAgainst,
          goalDifference: goalsFor - goalsAgainst
        },
        players: {
          totalGoals: playerStats._sum.goals || 0,
          totalAssists: playerStats._sum.assists || 0,
          totalYellowCards: playerStats._sum.yellowCards || 0,
          totalRedCards: playerStats._sum.redCards || 0,
          totalMinutesPlayed: playerStats._sum.minutesPlayed || 0
        },
        trainings: {
          total: trainingStats,
          attendanceRate: Math.round(attendanceRate)
        },
        topScorers,
        topAssisters,
        mostActivePlayer
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques de l\'équipe:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/stats/player/:id - Statistiques d'un joueur
router.get('/player/:id', requirePlayerOrAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { season = '2024-2025' } = req.query;

    // Vérifier que le joueur existe
    const player = await prisma.player.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            firstName: true,
            lastName: true,
            avatar: true
          }
        }
      }
    });

    if (!player) {
      return res.status(404).json({
        message: 'Joueur non trouvé'
      });
    }

    // Statistiques du joueur pour la saison
    const playerStats = await prisma.playerStats.findUnique({
      where: {
        playerId_season: {
          playerId: id,
          season
        }
      }
    });

    // Historique des matchs du joueur
    const matchHistory = await prisma.matchPlayer.findMany({
      where: { 
        playerId: id,
        match: {
          status: 'FINISHED',
          matchDate: {
            gte: new Date(`${season.split('-')[0]}-07-01`),
            lt: new Date(`${season.split('-')[1]}-07-01`)
          }
        }
      },
      include: {
        match: {
          select: {
            id: true,
            homeTeam: true,
            awayTeam: true,
            matchDate: true,
            homeScore: true,
            awayScore: true,
            isHome: true
          }
        }
      },
      orderBy: {
        match: {
          matchDate: 'desc'
        }
      }
    });

    // Présences aux entraînements
    const trainingAttendances = await prisma.trainingAttendance.findMany({
      where: {
        playerId: id,
        training: {
          date: {
            gte: new Date(`${season.split('-')[0]}-07-01`),
            lt: new Date(`${season.split('-')[1]}-07-01`)
          }
        }
      },
      include: {
        training: {
          select: {
            title: true,
            date: true,
            location: true
          }
        }
      }
    });

    const totalTrainings = trainingAttendances.length;
    const presentTrainings = trainingAttendances.filter(att => att.isPresent).length;
    const trainingAttendanceRate = totalTrainings > 0 ? (presentTrainings / totalTrainings) * 100 : 0;

    // Évolution des performances (derniers 10 matchs)
    const recentMatches = matchHistory.slice(0, 10);

    res.json({
      message: 'Statistiques du joueur récupérées avec succès',
      player,
      stats: {
        season,
        general: playerStats || {
          matchesPlayed: 0,
          goals: 0,
          assists: 0,
          yellowCards: 0,
          redCards: 0,
          minutesPlayed: 0
        },
        training: {
          total: totalTrainings,
          present: presentTrainings,
          attendanceRate: Math.round(trainingAttendanceRate)
        },
        matchHistory,
        recentMatches
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques du joueur:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

// GET /api/stats/dashboard - Statistiques pour le tableau de bord
router.get('/dashboard', requirePlayerOrAdmin, async (req, res) => {
  try {
    const currentDate = new Date();
    const currentSeason = '2024-2025';

    // Prochains matchs
    const upcomingMatches = await prisma.match.findMany({
      where: {
        matchDate: {
          gte: currentDate
        },
        status: 'SCHEDULED'
      },
      orderBy: {
        matchDate: 'asc'
      },
      take: 3
    });

    // Derniers résultats
    const recentResults = await prisma.match.findMany({
      where: {
        matchDate: {
          lt: currentDate
        },
        status: 'FINISHED'
      },
      orderBy: {
        matchDate: 'desc'
      },
      take: 3
    });

    // Prochains entraînements
    const upcomingTrainings = await prisma.training.findMany({
      where: {
        date: {
          gte: currentDate
        }
      },
      orderBy: {
        date: 'asc'
      },
      take: 3
    });

    // Statistiques rapides
    const totalPlayers = await prisma.player.count({
      where: { isActive: true }
    });

    const totalMatches = await prisma.match.count({
      where: {
        status: 'FINISHED',
        matchDate: {
          gte: new Date(`${currentSeason.split('-')[0]}-07-01`)
        }
      }
    });

    const totalGoals = await prisma.playerStats.aggregate({
      where: { season: currentSeason },
      _sum: { goals: true }
    });

    res.json({
      message: 'Statistiques du tableau de bord récupérées avec succès',
      dashboard: {
        upcomingMatches,
        recentResults,
        upcomingTrainings,
        quickStats: {
          totalPlayers,
          totalMatches,
          totalGoals: totalGoals._sum.goals || 0,
          currentSeason
        }
      }
    });

  } catch (error) {
    console.error('Erreur lors de la récupération des statistiques du tableau de bord:', error);
    res.status(500).json({
      message: 'Erreur interne du serveur'
    });
  }
});

module.exports = router;
