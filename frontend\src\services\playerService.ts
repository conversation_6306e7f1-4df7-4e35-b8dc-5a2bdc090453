// Service pour gérer les appels API des joueurs
// Correction: suppression de l'import authService problématique
const API_BASE_URL = '/api';

export interface PlayerData {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth: string;
  nationality: string;
  position: 'GOALKEEPER' | 'DEFENDER' | 'MIDFIELDER' | 'FORWARD';
  jerseyNumber: number;
  height: number;
  weight: number;
  preferredFoot: 'LEFT' | 'RIGHT' | 'BOTH';
  contractStart?: string;
  contractEnd?: string;
  salary?: number;
  biography?: string;
  previousClubs?: string;
  achievements?: string;
  profileImage?: string;
  isActive?: boolean;
}

export interface Player {
  id: string;
  user: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    avatar?: string;
    createdAt: string;
    updatedAt: string;
  };
  dateOfBirth: string;
  nationality: string;
  position: string;
  jerseyNumber: number;
  height: number;
  weight: number;
  preferredFoot: string;
  contractStart?: string;
  contractEnd?: string;
  salary?: number;
  biography?: string;
  previousClubs?: string;
  achievements?: string;
  profileImage?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T> {
  message: string;
  data?: T;
  player?: T;
  players?: T[];
  error?: string;
}

class PlayerService {
  private getAuthHeaders() {
    const token = localStorage.getItem('esc_auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
  }

  // Récupérer tous les joueurs
  async getAllPlayers(): Promise<Player[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/players`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data: ApiResponse<Player[]> = await response.json();
      return data.players || [];
    } catch (error) {
      console.error('Erreur lors de la récupération des joueurs:', error);
      throw error;
    }
  }

  // Récupérer un joueur par ID
  async getPlayerById(id: string): Promise<Player> {
    try {
      const response = await fetch(`${API_BASE_URL}/players/${id}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('Joueur non trouvé');
        }
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data: ApiResponse<Player> = await response.json();
      return data.player!;
    } catch (error) {
      console.error('Erreur lors de la récupération du joueur:', error);
      throw error;
    }
  }

  // Créer un nouveau joueur
  async createPlayer(playerData: PlayerData): Promise<Player> {
    try {
      const response = await fetch(`${API_BASE_URL}/players`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(playerData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Erreur HTTP: ${response.status}`);
      }

      const data: ApiResponse<Player> = await response.json();
      return data.player!;
    } catch (error) {
      console.error('Erreur lors de la création du joueur:', error);
      throw error;
    }
  }

  // Mettre à jour un joueur
  async updatePlayer(id: string, playerData: Partial<PlayerData>): Promise<Player> {
    try {
      const response = await fetch(`${API_BASE_URL}/players/${id}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(playerData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 404) {
          throw new Error('Joueur non trouvé');
        }
        if (response.status === 409) {
          throw new Error(errorData.message || 'Conflit de données');
        }
        throw new Error(errorData.message || `Erreur HTTP: ${response.status}`);
      }

      const data: ApiResponse<Player> = await response.json();
      return data.player!;
    } catch (error) {
      console.error('Erreur lors de la mise à jour du joueur:', error);
      throw error;
    }
  }

  // Supprimer un joueur
  async deletePlayer(id: string): Promise<void> {
    try {
      const response = await fetch(`${API_BASE_URL}/players/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 404) {
          throw new Error('Joueur non trouvé');
        }
        throw new Error(errorData.message || `Erreur HTTP: ${response.status}`);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du joueur:', error);
      throw error;
    }
  }

  // Activer/Désactiver un joueur
  async togglePlayerStatus(id: string, isActive: boolean): Promise<Player> {
    try {
      const response = await fetch(`${API_BASE_URL}/players/${id}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ isActive })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Erreur HTTP: ${response.status}`);
      }

      const data: ApiResponse<Player> = await response.json();
      return data.player!;
    } catch (error) {
      console.error('Erreur lors du changement de statut du joueur:', error);
      throw error;
    }
  }

  // Rechercher des joueurs
  async searchPlayers(query: string): Promise<Player[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/players/search?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data: ApiResponse<Player[]> = await response.json();
      return data.players || [];
    } catch (error) {
      console.error('Erreur lors de la recherche de joueurs:', error);
      throw error;
    }
  }

  // Obtenir les statistiques d'un joueur
  async getPlayerStats(id: string, season?: string): Promise<any> {
    try {
      const url = season
        ? `${API_BASE_URL}/players/${id}/stats?season=${season}`
        : `${API_BASE_URL}/players/${id}/stats`;

      const response = await fetch(url, {
        method: 'GET',
        headers: this.getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error(`Erreur HTTP: ${response.status}`);
      }

      const data = await response.json();
      return data.stats;
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  // Valider les données d'un joueur
  validatePlayerData(playerData: PlayerData): string[] {
    const errors: string[] = [];

    if (!playerData.firstName?.trim()) {
      errors.push('Le prénom est requis');
    }

    if (!playerData.lastName?.trim()) {
      errors.push('Le nom est requis');
    }

    if (!playerData.email?.trim()) {
      errors.push('L\'email est requis');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(playerData.email)) {
      errors.push('L\'email n\'est pas valide');
    }

    if (!playerData.dateOfBirth) {
      errors.push('La date de naissance est requise');
    } else {
      const birthDate = new Date(playerData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      if (age < 16 || age > 45) {
        errors.push('L\'âge doit être entre 16 et 45 ans');
      }
    }

    if (!playerData.position) {
      errors.push('La position est requise');
    }

    if (!playerData.jerseyNumber || playerData.jerseyNumber < 1 || playerData.jerseyNumber > 99) {
      errors.push('Le numéro de maillot doit être entre 1 et 99');
    }

    if (playerData.height && (playerData.height < 150 || playerData.height > 220)) {
      errors.push('La taille doit être entre 150 et 220 cm');
    }

    if (playerData.weight && (playerData.weight < 50 || playerData.weight > 120)) {
      errors.push('Le poids doit être entre 50 et 120 kg');
    }

    return errors;
  }
}

// Export de l'instance singleton
export const playerService = new PlayerService();
export default playerService;
