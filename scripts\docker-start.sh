#!/bin/bash

# Script de démarrage Docker pour Espoir Sportive Chorbane
# Usage: ./scripts/docker-start.sh [dev|prod|stop|clean]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifier que Docker est installé
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker n'est pas installé. Veuillez l'installer d'abord."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose n'est pas installé. Veuillez l'installer d'abord."
        exit 1
    fi
}

# Créer les dossiers nécessaires
create_directories() {
    log_info "Création des dossiers nécessaires..."
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/uploads
    mkdir -p data/logs
    mkdir -p nginx/ssl
    log_success "Dossiers créés avec succès"
}

# Copier le fichier d'environnement
setup_env() {
    if [ ! -f .env ]; then
        log_info "Copie du fichier d'environnement..."
        cp .env.docker .env
        log_warning "Fichier .env créé. Veuillez le modifier selon vos besoins."
    else
        log_info "Fichier .env existe déjà"
    fi
}

# Démarrage en mode développement
start_dev() {
    log_info "Démarrage en mode développement..."
    
    create_directories
    setup_env
    
    log_info "Construction et démarrage des conteneurs de développement..."
    docker-compose -f docker-compose.dev.yml up --build -d
    
    log_success "Environnement de développement démarré !"
    log_info "Services disponibles :"
    echo "  - Frontend: http://localhost:3001"
    echo "  - Backend API: http://localhost:5001"
    echo "  - Adminer (DB): http://localhost:8081"
    echo "  - MailHog: http://localhost:8025"
    echo "  - Redis: localhost:6380"
    
    log_info "Pour voir les logs : docker-compose -f docker-compose.dev.yml logs -f"
}

# Démarrage en mode production
start_prod() {
    log_info "Démarrage en mode production..."
    
    create_directories
    setup_env
    
    log_info "Construction et démarrage des conteneurs de production..."
    docker-compose up --build -d
    
    log_success "Environnement de production démarré !"
    log_info "Services disponibles :"
    echo "  - Application: http://localhost:3000"
    echo "  - API: http://localhost:5000"
    echo "  - Nginx: http://localhost:80"
    
    log_info "Pour voir les logs : docker-compose logs -f"
}

# Arrêt des services
stop_services() {
    log_info "Arrêt des services..."
    
    if [ -f docker-compose.dev.yml ]; then
        docker-compose -f docker-compose.dev.yml down
    fi
    
    if [ -f docker-compose.yml ]; then
        docker-compose down
    fi
    
    log_success "Services arrêtés"
}

# Nettoyage complet
clean_all() {
    log_warning "Nettoyage complet (suppression des volumes et images)..."
    read -p "Êtes-vous sûr ? Cette action est irréversible. (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        stop_services
        
        log_info "Suppression des volumes..."
        docker volume prune -f
        
        log_info "Suppression des images..."
        docker image prune -a -f
        
        log_info "Suppression des réseaux..."
        docker network prune -f
        
        log_success "Nettoyage terminé"
    else
        log_info "Nettoyage annulé"
    fi
}

# Affichage des logs
show_logs() {
    local service=$2
    
    if [ "$1" = "dev" ]; then
        if [ -n "$service" ]; then
            docker-compose -f docker-compose.dev.yml logs -f "$service"
        else
            docker-compose -f docker-compose.dev.yml logs -f
        fi
    else
        if [ -n "$service" ]; then
            docker-compose logs -f "$service"
        else
            docker-compose logs -f
        fi
    fi
}

# Fonction d'aide
show_help() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commandes disponibles :"
    echo "  dev         Démarrer en mode développement"
    echo "  prod        Démarrer en mode production"
    echo "  stop        Arrêter tous les services"
    echo "  clean       Nettoyage complet (volumes, images, réseaux)"
    echo "  logs [env]  Afficher les logs (dev ou prod)"
    echo "  help        Afficher cette aide"
    echo ""
    echo "Exemples :"
    echo "  $0 dev                    # Démarrer en développement"
    echo "  $0 prod                   # Démarrer en production"
    echo "  $0 logs dev               # Logs de développement"
    echo "  $0 logs prod backend      # Logs du backend en production"
}

# Script principal
main() {
    check_docker
    
    case "${1:-help}" in
        "dev")
            start_dev
            ;;
        "prod")
            start_prod
            ;;
        "stop")
            stop_services
            ;;
        "clean")
            clean_all
            ;;
        "logs")
            show_logs "$2" "$3"
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Exécution du script
main "$@"
