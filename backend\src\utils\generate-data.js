const { PrismaClient } = require('@prisma/client');
const {
  TUNISIAN_DATA,
  getRandomElement,
  generateTunisianName,
  generateRealisticEmail,
  generateT<PERSON>sianPhone,
  generateFootballArticleTitle,
  generateRealisticMatchScore,
  faker
} = require('./faker-helpers');

const prisma = new PrismaClient();

/**
 * Génère des articles supplémentaires
 * @param {number} count - Nombre d'articles à générer
 */
async function generateArticles(count = 10) {
  console.log(`📰 Génération de ${count} articles...`);
  
  const admin = await prisma.user.findFirst({ where: { role: 'ADMIN' } });
  if (!admin) {
    throw new Error('Aucun administrateur trouvé');
  }

  const players = await prisma.player.findMany({
    include: { user: true }
  });

  const articleTypes = ['match_result', 'recruitment', 'training', 'interview', 'general'];
  
  for (let i = 0; i < count; i++) {
    const articleType = getRandomElement(articleTypes);
    
    let context = {};
    if (articleType === 'match_result') {
      context.team = getRandomElement(TUNISIAN_DATA.footballClubs);
      const score = generateRealisticMatchScore();
      context.score = `${score.homeScore}-${score.awayScore}`;
    } else if (articleType === 'interview' || articleType === 'recruitment') {
      const randomPlayer = getRandomElement(players);
      context.player = `${randomPlayer.user.firstName} ${randomPlayer.user.lastName}`;
    }
    
    const title = generateFootballArticleTitle(articleType, context);
    const content = faker.lorem.paragraphs({ min: 4, max: 10 }, '\n\n');
    const excerpt = faker.lorem.paragraph({ min: 1, max: 2 });
    const isPublished = faker.datatype.boolean({ probability: 0.85 });
    
    await prisma.article.create({
      data: {
        title,
        content,
        excerpt,
        isPublished,
        authorId: admin.id,
        coverImage: faker.datatype.boolean({ probability: 0.7 }) 
          ? faker.image.urlLoremFlickr({ category: 'sports' }) 
          : null
      }
    });
  }
  
  console.log(`✅ ${count} articles générés`);
}

/**
 * Génère des matchs supplémentaires
 * @param {number} count - Nombre de matchs à générer
 */
async function generateMatches(count = 10) {
  console.log(`⚽ Génération de ${count} matchs...`);
  
  for (let i = 0; i < count; i++) {
    const matchDate = faker.date.between({
      from: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), // -6 mois
      to: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000)     // +3 mois
    });
    
    const isFinished = matchDate < new Date();
    const isHome = faker.datatype.boolean();
    const awayTeam = getRandomElement(TUNISIAN_DATA.footballClubs.filter(team => team !== 'Espoir Sportive Chorbane'));
    const location = isHome ? 'Stade Municipal Chorbane' : getRandomElement(TUNISIAN_DATA.stadiums);
    
    let homeScore = null;
    let awayScore = null;
    let status = 'SCHEDULED';
    
    if (isFinished) {
      const score = generateRealisticMatchScore();
      homeScore = isHome ? score.homeScore : score.awayScore;
      awayScore = isHome ? score.awayScore : score.homeScore;
      status = 'FINISHED';
    }
    
    await prisma.match.create({
      data: {
        homeTeam: isHome ? 'Espoir Sportive Chorbane' : awayTeam,
        awayTeam: isHome ? awayTeam : 'Espoir Sportive Chorbane',
        matchDate,
        location,
        homeScore,
        awayScore,
        status,
        description: faker.lorem.sentence({ min: 5, max: 10 }),
        isHome
      }
    });
  }
  
  console.log(`✅ ${count} matchs générés`);
}

/**
 * Génère des supporters supplémentaires
 * @param {number} count - Nombre de supporters à générer
 */
async function generateSupporters(count = 20) {
  console.log(`🎉 Génération de ${count} supporters...`);
  
  const { hashPassword } = require('./auth');
  const supporterPassword = await hashPassword('supporter123');
  
  for (let i = 0; i < count; i++) {
    const { firstName, lastName } = generateTunisianName();
    const email = generateRealisticEmail(firstName, lastName);
    
    await prisma.user.create({
      data: {
        email,
        password: supporterPassword,
        firstName,
        lastName,
        role: 'SUPPORTER',
        phone: generateTunisianPhone(),
        avatar: faker.image.avatar()
      }
    });
  }
  
  console.log(`✅ ${count} supporters générés`);
}

/**
 * Génère des commentaires sur les articles existants
 * @param {number} maxCommentsPerArticle - Nombre maximum de commentaires par article
 */
async function generateComments(maxCommentsPerArticle = 5) {
  console.log('💬 Génération de commentaires...');
  
  const publishedArticles = await prisma.article.findMany({
    where: { isPublished: true }
  });
  
  const users = await prisma.user.findMany();
  let totalComments = 0;
  
  for (const article of publishedArticles) {
    const commentCount = faker.number.int({ min: 0, max: maxCommentsPerArticle });
    
    for (let i = 0; i < commentCount; i++) {
      const randomUser = getRandomElement(users);
      
      await prisma.comment.create({
        data: {
          content: faker.lorem.paragraph({ min: 1, max: 3 }),
          articleId: article.id,
          authorId: randomUser.id
        }
      });
      
      totalComments++;
    }
  }
  
  console.log(`✅ ${totalComments} commentaires générés`);
}

/**
 * Génère des événements pour les matchs terminés
 */
async function generateMatchEvents() {
  console.log('⚽ Génération d\'événements de match...');
  
  const finishedMatches = await prisma.match.findMany({
    where: { status: 'FINISHED' }
  });
  
  const players = await prisma.player.findMany();
  let totalEvents = 0;
  
  for (const match of finishedMatches) {
    const eventCount = faker.number.int({ min: 2, max: 8 });
    
    for (let i = 0; i < eventCount; i++) {
      const eventTypes = ['GOAL', 'YELLOW_CARD', 'RED_CARD', 'SUBSTITUTION'];
      const eventType = getRandomElement(eventTypes);
      const minute = faker.number.int({ min: 1, max: 90 });
      const randomPlayer = getRandomElement(players);
      
      let cardType = null;
      if (eventType === 'YELLOW_CARD') cardType = 'YELLOW';
      if (eventType === 'RED_CARD') cardType = 'RED';
      
      await prisma.matchEvent.create({
        data: {
          matchId: match.id,
          playerId: randomPlayer.id,
          minute,
          type: eventType,
          cardType,
          description: faker.lorem.sentence({ min: 3, max: 6 })
        }
      });
      
      totalEvents++;
    }
  }
  
  console.log(`✅ ${totalEvents} événements de match générés`);
}

/**
 * Script principal
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const count = parseInt(args[1]) || 10;
  
  try {
    switch (command) {
      case 'articles':
        await generateArticles(count);
        break;
      case 'matches':
        await generateMatches(count);
        break;
      case 'supporters':
        await generateSupporters(count);
        break;
      case 'comments':
        await generateComments(count);
        break;
      case 'events':
        await generateMatchEvents();
        break;
      case 'all':
        await generateArticles(5);
        await generateMatches(5);
        await generateSupporters(10);
        await generateComments(3);
        await generateMatchEvents();
        break;
      default:
        console.log('Usage: node generate-data.js <command> [count]');
        console.log('Commands:');
        console.log('  articles [count]  - Générer des articles (défaut: 10)');
        console.log('  matches [count]   - Générer des matchs (défaut: 10)');
        console.log('  supporters [count] - Générer des supporters (défaut: 20)');
        console.log('  comments [max]    - Générer des commentaires (max par article: défaut 5)');
        console.log('  events           - Générer des événements de match');
        console.log('  all              - Générer un peu de tout');
        break;
    }
  } catch (error) {
    console.error('❌ Erreur:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main()
    .finally(async () => {
      await prisma.$disconnect();
    });
}

module.exports = {
  generateArticles,
  generateMatches,
  generateSupporters,
  generateComments,
  generateMatchEvents
};
