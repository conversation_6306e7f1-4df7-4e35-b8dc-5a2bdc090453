{"name": "espoir-sportive-backend", "version": "1.0.0", "description": "Backend API pour l'application de gestion d'équipe de football Espoir Sportive Chorbane", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "node src/utils/seed.js", "db:seed-advanced": "node src/utils/seed-advanced.js", "db:generate-data": "node src/utils/generate-data.js", "db:add-images": "node src/utils/add-images.js", "db:download-images": "node src/utils/download-images.js", "db:reset": "prisma migrate reset --force && npm run db:seed", "db:reset-advanced": "prisma migrate reset --force && npm run db:seed-advanced", "db:deploy": "prisma migrate deploy", "db:push": "prisma db push", "lint": "echo \"<PERSON><PERSON> not configured yet\"", "format": "echo \"Formatting not configured yet\"", "build": "echo \"Build process not configured yet\"", "logs": "tail -f logs/app.log", "clean": "rm -rf node_modules package-lock.json && npm install"}, "keywords": ["football", "team-management", "sports", "api"], "author": "Espoir Sportive Chorbane", "license": "MIT", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0"}, "devDependencies": {"@faker-js/faker": "^9.8.0", "@prisma/client": "^6.8.2", "nodemon": "^3.1.10", "prisma": "^6.8.2"}}