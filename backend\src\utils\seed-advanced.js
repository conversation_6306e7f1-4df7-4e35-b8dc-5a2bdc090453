const { PrismaClient } = require('@prisma/client');
const { hashPassword } = require('./auth');
const {
  TUNISIAN_DATA,
  getRandomElement,
  generateTunisianName,
  generateRealisticEmail,
  generateTunisianPhone,
  generatePlayerPhysicalStats,
  generatePlayerGameStats,
  generateFootballArticleTitle,
  generateRealisticMatchScore,
  faker
} = require('./faker-helpers');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Début du seeding avancé avec Faker...');

  try {
    // Nettoyer la base de données
    console.log('🧹 Nettoyage de la base de données...');
    await prisma.galleryMedia.deleteMany();
    await prisma.gallery.deleteMany();
    await prisma.articleTag.deleteMany();
    await prisma.tag.deleteMany();
    await prisma.comment.deleteMany();
    await prisma.article.deleteMany();
    await prisma.trainingAttendance.deleteMany();
    await prisma.training.deleteMany();
    await prisma.matchEvent.deleteMany();
    await prisma.matchPlayer.deleteMany();
    await prisma.match.deleteMany();
    await prisma.playerStats.deleteMany();
    await prisma.player.deleteMany();
    await prisma.user.deleteMany();

    console.log('✅ Base de données nettoyée');

    // Créer l'administrateur
    const adminPassword = await hashPassword('admin123');
    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: adminPassword,
        firstName: 'Admin',
        lastName: 'Espoir Sportive',
        role: 'ADMIN',
        phone: generateTunisianPhone(),
        avatar: faker.image.avatar()
      }
    });

    console.log('👤 Administrateur créé');

    // Créer des joueurs réalistes
    const positionCounts = { 
      GOALKEEPER: 3, 
      DEFENDER: 8, 
      MIDFIELDER: 10, 
      FORWARD: 7 
    };
    
    const playerPassword = await hashPassword('player123');
    const players = [];
    let jerseyNumber = 1;

    for (const [position, count] of Object.entries(positionCounts)) {
      for (let i = 0; i < count; i++) {
        const { firstName, lastName } = generateTunisianName();
        const email = generateRealisticEmail(firstName, lastName);
        const physicalStats = generatePlayerPhysicalStats(position);
        
        // Créer l'utilisateur
        const user = await prisma.user.create({
          data: {
            email,
            password: playerPassword,
            firstName,
            lastName,
            role: 'PLAYER',
            phone: generateTunisianPhone(),
            avatar: faker.image.avatar()
          }
        });

        // Créer le joueur
        const player = await prisma.player.create({
          data: {
            userId: user.id,
            jerseyNumber,
            position,
            height: physicalStats.height,
            weight: physicalStats.weight,
            birthDate: faker.date.between({ 
              from: new Date('1995-01-01'), 
              to: new Date('2006-12-31') 
            }),
            nationality: 'Tunisie',
            joinDate: faker.date.between({ 
              from: new Date('2019-01-01'), 
              to: new Date('2024-01-01') 
            }),
            biography: faker.lorem.paragraph({ min: 2, max: 4 })
          }
        });

        // Créer des statistiques pour plusieurs saisons
        const seasons = ['2022-2023', '2023-2024', '2024-2025'];
        for (const season of seasons) {
          const gameStats = generatePlayerGameStats(position);
          
          await prisma.playerStats.create({
            data: {
              playerId: player.id,
              season,
              ...gameStats
            }
          });
        }

        players.push(player);
        jerseyNumber++;
      }
    }

    // Créer le joueur de test spécifique
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: playerPassword,
        firstName: 'Hamza',
        lastName: 'Bedoui',
        role: 'PLAYER',
        phone: generateTunisianPhone(),
        avatar: faker.image.avatar()
      }
    });

    const testPlayer = await prisma.player.create({
      data: {
        userId: testUser.id,
        jerseyNumber: 99,
        position: 'MIDFIELDER',
        height: 1.75,
        weight: 72.5,
        birthDate: new Date('1995-05-15'),
        nationality: 'Tunisie',
        joinDate: new Date('2020-07-01'),
        biography: 'Milieu de terrain technique et créatif, capitaine de l\'équipe.'
      }
    });

    // Statistiques pour le joueur de test
    const seasons = ['2022-2023', '2023-2024', '2024-2025'];
    for (const season of seasons) {
      await prisma.playerStats.create({
        data: {
          playerId: testPlayer.id,
          season,
          matchesPlayed: faker.number.int({ min: 15, max: 25 }),
          goals: faker.number.int({ min: 5, max: 12 }),
          assists: faker.number.int({ min: 8, max: 15 }),
          yellowCards: faker.number.int({ min: 2, max: 6 }),
          redCards: faker.number.int({ min: 0, max: 1 }),
          minutesPlayed: faker.number.int({ min: 1200, max: 2000 })
        }
      });
    }

    players.push(testPlayer);

    console.log(`⚽ ${players.length} joueurs créés`);

    // Créer des supporters
    const supporterPassword = await hashPassword('supporter123');
    const supportersCount = 25;
    
    for (let i = 0; i < supportersCount; i++) {
      const { firstName, lastName } = generateTunisianName();
      const email = generateRealisticEmail(firstName, lastName);
      
      await prisma.user.create({
        data: {
          email,
          password: supporterPassword,
          firstName,
          lastName,
          role: 'SUPPORTER',
          phone: generateTunisianPhone(),
          avatar: faker.image.avatar()
        }
      });
    }

    // Supporter de test
    await prisma.user.create({
      data: {
        email: '<EMAIL>',
        password: supporterPassword,
        firstName: 'Supporter',
        lastName: 'Fidèle',
        role: 'SUPPORTER',
        phone: generateTunisianPhone(),
        avatar: faker.image.avatar()
      }
    });

    console.log('🎉 Supporters créés');

    // Créer des matchs réalistes
    const matches = [];
    const matchCount = 25;
    
    for (let i = 0; i < matchCount; i++) {
      const matchDate = faker.date.between({
        from: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000), // -4 mois
        to: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000)     // +2 mois
      });
      
      const isFinished = matchDate < new Date();
      const isHome = faker.datatype.boolean();
      const awayTeam = getRandomElement(TUNISIAN_DATA.footballClubs.filter(team => team !== 'Espoir Sportive Chorbane'));
      const location = isHome ? 'Stade Municipal Chorbane' : getRandomElement(TUNISIAN_DATA.stadiums);
      
      let homeScore = null;
      let awayScore = null;
      let status = 'SCHEDULED';
      
      if (isFinished) {
        const score = generateRealisticMatchScore();
        homeScore = isHome ? score.homeScore : score.awayScore;
        awayScore = isHome ? score.awayScore : score.homeScore;
        status = 'FINISHED';
      } else if (faker.number.float() < 0.05) { // 5% de chance d'être en cours
        status = 'LIVE';
        const score = generateRealisticMatchScore();
        homeScore = isHome ? score.homeScore : score.awayScore;
        awayScore = isHome ? score.awayScore : score.homeScore;
      }
      
      const match = await prisma.match.create({
        data: {
          homeTeam: isHome ? 'Espoir Sportive Chorbane' : awayTeam,
          awayTeam: isHome ? awayTeam : 'Espoir Sportive Chorbane',
          matchDate,
          location,
          homeScore,
          awayScore,
          status,
          description: faker.lorem.sentence({ min: 5, max: 10 }),
          isHome
        }
      });
      matches.push(match);
    }

    console.log('🏆 Matchs créés');

    // Créer des entraînements
    const trainingTypes = [
      'Entraînement technique', 'Préparation physique', 'Tactique défensive',
      'Tactique offensive', 'Séance de tirs', 'Travail de passes',
      'Condition physique', 'Match d\'entraînement', 'Échauffement',
      'Récupération active'
    ];

    for (let i = 0; i < 30; i++) {
      const trainingDate = faker.date.between({
        from: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // -3 mois
        to: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)    // +1 mois
      });
      
      const training = await prisma.training.create({
        data: {
          title: getRandomElement(trainingTypes),
          description: faker.lorem.paragraph({ min: 1, max: 3 }),
          date: trainingDate,
          location: getRandomElement(['Terrain d\'entraînement Chorbane', 'Stade Municipal Chorbane', 'Terrain annexe']),
          duration: faker.number.int({ min: 60, max: 120 })
        }
      });

      // Présences réalistes
      for (const player of players) {
        const isPresent = faker.datatype.boolean({ probability: 0.88 });
        
        await prisma.trainingAttendance.create({
          data: {
            trainingId: training.id,
            playerId: player.id,
            isPresent,
            notes: isPresent && faker.datatype.boolean({ probability: 0.25 }) 
              ? faker.helpers.arrayElement([
                  'Excellente performance', 'Bonne séance', 'Très motivé',
                  'Progression notable', 'Besoin de repos', 'Performance moyenne'
                ])
              : null
          }
        });
      }
    }

    console.log('🏃 Entraînements créés');

    console.log('✅ Seeding avancé terminé avec succès !');
    console.log('\n📊 Statistiques générées :');
    console.log(`👥 ${players.length} joueurs`);
    console.log(`🎉 ${supportersCount + 1} supporters`);
    console.log(`⚽ ${matches.length} matchs`);
    console.log('🏃 30 entraînements');
    
    console.log('\n📋 Comptes de test :');
    console.log('👨‍💼 Admin: <EMAIL> / admin123');
    console.log('⚽ Joueur: <EMAIL> / player123');
    console.log('🎉 Supporter: <EMAIL> / supporter123');

  } catch (error) {
    console.error('❌ Erreur lors du seeding:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
