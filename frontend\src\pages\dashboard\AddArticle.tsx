import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Save,
  ArrowLeft,
  Upload,
  Eye,
  Tag,
  FileText,
  Image,
  Globe
} from 'lucide-react';
import DashboardLayout from '../../components/dashboard/DashboardLayout';

interface ArticleFormData {
  title: string;
  content: string;
  excerpt: string;
  category: string;
  status: 'DRAFT' | 'PUBLISHED';
  tags: string[];
  metaDescription: string;
  slug: string;
}

const AddArticle: React.FC = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState<ArticleFormData>({
    title: '',
    content: '',
    excerpt: '',
    category: 'MATCH',
    status: 'DRAFT',
    tags: [],
    metaDescription: '',
    slug: ''
  });

  const [newTag, setNewTag] = useState('');
  const [saving, setSaving] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  const categories = [
    { value: 'MATCH', label: 'Match', color: 'bg-blue-100 text-blue-800' },
    { value: 'TRANSFERT', label: 'Transfert', color: 'bg-purple-100 text-purple-800' },
    { value: 'ENTRAINEMENT', label: 'Entraînement', color: 'bg-orange-100 text-orange-800' },
    { value: 'CLUB', label: 'Club', color: 'bg-green-100 text-green-800' },
    { value: 'COMMUNAUTE', label: 'Communauté', color: 'bg-pink-100 text-pink-800' }
  ];

  const handleChange = (field: keyof ArticleFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Auto-generate slug from title
    if (field === 'title') {
      const slug = value
        .toLowerCase()
        .replace(/[àáâãäå]/g, 'a')
        .replace(/[èéêë]/g, 'e')
        .replace(/[ìíîï]/g, 'i')
        .replace(/[òóôõö]/g, 'o')
        .replace(/[ùúûü]/g, 'u')
        .replace(/[ç]/g, 'c')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim();
      setFormData(prev => ({ ...prev, slug }));
    }
  };

  const handleAddTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Article créé avec succès !');
      navigate('/dashboard/news');
    } catch (error) {
      alert('Erreur lors de la création de l\'article');
    } finally {
      setSaving(false);
    }
  };

  const getCategoryInfo = (categoryValue: string) => {
    return categories.find(cat => cat.value === categoryValue) || categories[0];
  };

  const renderPreview = () => {
    return (
      <div className="prose max-w-none">
        <h1 className="text-3xl font-bold mb-4">{formData.title || 'Titre de l\'article'}</h1>
        <p className="text-lg text-gray-600 mb-6">{formData.excerpt}</p>
        <div className="border-t pt-6">
          {formData.content.split('\n').map((paragraph, index) => {
            if (paragraph.startsWith('# ')) {
              return <h1 key={index} className="text-2xl font-bold mb-4">{paragraph.slice(2)}</h1>;
            }
            if (paragraph.startsWith('## ')) {
              return <h2 key={index} className="text-xl font-semibold mb-3">{paragraph.slice(3)}</h2>;
            }
            if (paragraph.startsWith('### ')) {
              return <h3 key={index} className="text-lg font-medium mb-2">{paragraph.slice(4)}</h3>;
            }
            if (paragraph.startsWith('- ')) {
              return <li key={index} className="mb-1">{paragraph.slice(2)}</li>;
            }
            if (paragraph.trim()) {
              return <p key={index} className="mb-4">{paragraph}</p>;
            }
            return <br key={index} />;
          })}
        </div>
      </div>
    );
  };

  return (
    <DashboardLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/dashboard/news')}
                className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Nouvel article</h1>
                <p className="mt-1 text-sm text-gray-600">
                  Créez un nouvel article pour le site d'Espoir Sportif de Chorbane
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => setPreviewMode(!previewMode)}
                className="btn btn-outline"
              >
                <Eye className="h-4 w-4 mr-2" />
                {previewMode ? 'Éditer' : 'Aperçu'}
              </button>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {!previewMode ? (
                <>
                  {/* Title */}
                  <div className="bg-white shadow rounded-lg p-6">
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Titre de l'article *
                      </label>
                      <input
                        type="text"
                        value={formData.title}
                        onChange={(e) => handleChange('title', e.target.value)}
                        className="input text-lg font-medium"
                        placeholder="Entrez le titre de l'article..."
                        required
                      />
                    </div>

                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        URL (slug)
                      </label>
                      <div className="flex items-center">
                        <span className="text-sm text-gray-500 mr-2">
                          /articles/
                        </span>
                        <input
                          type="text"
                          value={formData.slug}
                          onChange={(e) => handleChange('slug', e.target.value)}
                          className="input flex-1"
                          placeholder="url-de-l-article"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Résumé *
                      </label>
                      <textarea
                        value={formData.excerpt}
                        onChange={(e) => handleChange('excerpt', e.target.value)}
                        rows={3}
                        className="input"
                        placeholder="Résumé de l'article qui apparaîtra dans les listes..."
                        required
                      />
                      <p className="mt-1 text-sm text-gray-500">
                        {formData.excerpt.length}/200 caractères
                      </p>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="bg-white shadow rounded-lg p-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Contenu de l'article *
                    </label>
                    <textarea
                      value={formData.content}
                      onChange={(e) => handleChange('content', e.target.value)}
                      rows={20}
                      className="input font-mono text-sm"
                      placeholder="Rédigez le contenu de l'article en Markdown...

Exemples de formatage :
# Titre principal
## Sous-titre
### Titre de section

**Texte en gras**
*Texte en italique*

- Liste à puces
- Élément 2

1. Liste numérotée
2. Élément 2"
                      required
                    />
                    <p className="mt-2 text-sm text-gray-500">
                      Vous pouvez utiliser Markdown pour formater le texte
                    </p>
                  </div>
                </>
              ) : (
                <div className="bg-white shadow rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Aperçu de l'article</h3>
                  <div className="border rounded-lg p-6 bg-gray-50">
                    {renderPreview()}
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Publication Settings */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Publication</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Statut
                    </label>
                    <select
                      value={formData.status}
                      onChange={(e) => handleChange('status', e.target.value)}
                      className="input"
                    >
                      <option value="DRAFT">Brouillon</option>
                      <option value="PUBLISHED">Publier maintenant</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Catégorie
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleChange('category', e.target.value)}
                      className="input"
                    >
                      {categories.map(category => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                    <div className="mt-2">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryInfo(formData.category).color}`}>
                        {getCategoryInfo(formData.category).label}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Featured Image */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Image à la une</h3>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Image className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-2">
                    <button
                      type="button"
                      className="btn btn-outline btn-sm"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Ajouter une image
                    </button>
                  </div>
                  <p className="mt-2 text-xs text-gray-500">
                    JPG, PNG jusqu'à 2MB
                  </p>
                </div>
              </div>

              {/* Tags */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Tags</h3>
                <div className="space-y-4">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), handleAddTag())}
                      className="input flex-1"
                      placeholder="Ajouter un tag..."
                    />
                    <button
                      type="button"
                      onClick={handleAddTag}
                      className="btn btn-outline btn-sm"
                    >
                      <Tag className="h-4 w-4" />
                    </button>
                  </div>
                  
                  {formData.tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                          <button
                            type="button"
                            onClick={() => handleRemoveTag(tag)}
                            className="ml-1 text-gray-400 hover:text-gray-600"
                          >
                            ×
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {/* SEO */}
              <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">SEO</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Meta description
                  </label>
                  <textarea
                    value={formData.metaDescription}
                    onChange={(e) => handleChange('metaDescription', e.target.value)}
                    rows={3}
                    className="input"
                    placeholder="Description pour les moteurs de recherche..."
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    {formData.metaDescription.length}/160 caractères
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pb-8">
            <button
              type="button"
              onClick={() => navigate('/dashboard/news')}
              className="btn btn-outline"
            >
              Annuler
            </button>
            <button
              type="submit"
              disabled={saving}
              className="btn btn-primary"
            >
              {saving ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Création...
                </div>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Créer l'article
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default AddArticle;
